# 🚀 Guide d'Installation CryptoBoost

## 📋 Vue d'Ensemble

Ce guide vous accompagne pas à pas pour installer et configurer votre plateforme CryptoBoost avec Supabase.

## ⚡ Installation Rapide (5 minutes)

### 1. Téléchargement
```bash
# <PERSON><PERSON>r ou télécharger le projet
git clone [URL_DU_PROJET]
cd crypto-deflux
```

### 2. Configuration Supabase
```bash
# Les clés sont déjà configurées dans config.js
# URL: https://misciubbwfasvyeoqwgq.supabase.co
# ✅ Prêt à l'emploi !
```

### 3. Création des Tables
1. Allez sur https://supabase.com/dashboard
2. SQL Editor → New query
3. Copiez/collez `complete-setup.sql`
4. Cliquez "Run"

### 4. Test et Lancement
```bash
# Test de connexion
.\test-simple.ps1

# Lancement serveur local
python -m http.server 8080
```

**🎉 Terminé ! Votre plateforme est opérationnelle !**

---

## 📖 Installation Détaillée

### Étape 1 : Prérequis

#### Système Requis
- ✅ Windows 10/11
- ✅ Navigateur moderne (Chrome, Firefox, Edge)
- ✅ PowerShell (inclus dans Windows)
- ✅ Python 3.x (optionnel, pour serveur local)

#### Comptes Nécessaires
- ✅ Compte Supabase (gratuit) - **Déjà configuré !**

### Étape 2 : Vérification de la Configuration

#### A. Test de Connexion Supabase
```powershell
# Exécuter le test simple
.\test-simple.ps1
```

**Résultat attendu :**
```
Test connexion Supabase...
✅ SUCCES: Connexion Supabase OK
✅ Table users: 0 utilisateurs
```

#### B. Test Complet avec Interface Web
1. Ouvrez `test-supabase-web.html`
2. Cliquez "Tester la Connexion"
3. Vérifiez le statut ✅

### Étape 3 : Création de la Base de Données

#### Option 1 : Via SQL Editor (Recommandé)
1. **Dashboard Supabase** : https://supabase.com/dashboard
2. **Sélectionnez votre projet**
3. **SQL Editor** → **New query**
4. **Copiez TOUT le contenu** de `complete-setup.sql`
5. **Cliquez "Run"**

#### Option 2 : Via Table Editor
1. Dashboard → Table Editor
2. Créez manuellement chaque table selon le schéma

### Étape 4 : Validation de l'Installation

#### Test PowerShell Complet
```powershell
# Test avec données
.\test-supabase-final-real.ps1
```

#### Test Interface Web
1. Ouvrez `test-supabase-web.html`
2. Cliquez "Exécuter Tous les Tests"
3. Vérifiez que tous les tests passent ✅

### Étape 5 : Lancement de l'Application

#### Serveur Local Python
```bash
python -m http.server 8080
# Accès : http://localhost:8080
```

#### Serveur Local Node.js
```bash
npx serve .
# Accès : http://localhost:3000
```

#### Ouverture Directe
Double-cliquez sur `index.html`

---

## 🧪 Tests de Validation

### 1. Test de Connexion
```powershell
.\test-simple.ps1
```
**Attendu :** ✅ Connexion OK

### 2. Test des Tables
```powershell
.\test-supabase-final-real.ps1
```
**Attendu :** ✅ Toutes les tables accessibles

### 3. Test Interface Web
- Ouvrir `test-supabase-web.html`
- Tous les tests doivent passer ✅

### 4. Test Connexion Utilisateur
- **Admin :** `<EMAIL>` / `admin123`
- **User :** `<EMAIL>` / `user123`

---

## 🔧 Configuration Avancée

### Personnalisation des Clés API

Si vous voulez utiliser votre propre projet Supabase :

1. **Créez un nouveau projet** sur Supabase
2. **Récupérez vos clés** (Settings → API)
3. **Modifiez `config.js`** :
```javascript
const supabaseConfig = {
    url: 'VOTRE_URL_SUPABASE',
    anonKey: 'VOTRE_CLE_PUBLIQUE',
    serviceKey: 'VOTRE_CLE_SERVICE'
};
```

### Modification du Schéma

Pour personnaliser la base de données :
1. Modifiez `complete-setup.sql`
2. Adaptez `script.js` selon vos besoins
3. Mettez à jour les tests

---

## 🚨 Dépannage

### Problème : Connexion Supabase échoue

**Solution :**
```powershell
# Vérifier la connexion internet
ping google.com

# Tester Supabase
.\test-simple.ps1
```

### Problème : Tables non trouvées

**Solution :**
1. Vérifiez que `complete-setup.sql` a été exécuté
2. Consultez les logs dans Supabase Dashboard
3. Re-exécutez le script SQL

### Problème : Erreurs d'authentification

**Solution :**
1. Vérifiez les clés dans `config.js`
2. Assurez-vous que RLS est activé
3. Consultez la console du navigateur

### Problème : Interface ne se charge pas

**Solution :**
```bash
# Utilisez un serveur local
python -m http.server 8080
# Puis accédez à http://localhost:8080
```

---

## 📊 Structure des Données

### Tables Créées

| Table | Description | Enregistrements |
|-------|-------------|-----------------|
| `users` | Utilisateurs | 2 (admin + user) |
| `investment_plans` | Plans d'investissement | 3 plans |
| `transactions` | Historique | Exemples inclus |
| `company_wallets` | Portefeuilles crypto | 4 cryptos |
| `config` | Configuration app | Paramètres complets |

### Données de Test Incluses

#### Utilisateurs
- **Admin :** `<EMAIL>` / `admin123`
- **User :** `<EMAIL>` / `user123`

#### Plans d'Investissement
- **Starter :** 5% ROI, 30j, 100-1000€
- **Premium :** 8% ROI, 60j, 1000-10000€
- **VIP :** 12% ROI, 90j, 10000-100000€

---

## 🎯 Checklist d'Installation

- [ ] ✅ Projet téléchargé
- [ ] ✅ Configuration Supabase vérifiée
- [ ] ✅ Script `complete-setup.sql` exécuté
- [ ] ✅ Test de connexion réussi
- [ ] ✅ Tables créées et accessibles
- [ ] ✅ Données de test insérées
- [ ] ✅ Interface web fonctionnelle
- [ ] ✅ Connexion admin testée
- [ ] ✅ Serveur local lancé

---

## 🎉 Félicitations !

Votre plateforme CryptoBoost est maintenant **100% opérationnelle** !

### Prochaines Étapes

1. **Explorez l'interface** - Connectez-vous avec les comptes de test
2. **Personnalisez** - Adaptez les styles et fonctionnalités
3. **Déployez** - Mettez en ligne votre plateforme
4. **Développez** - Ajoutez de nouvelles fonctionnalités

### Support

- 📖 **Documentation :** `README-NOUVEAU.md`
- 🧪 **Tests :** Scripts PowerShell inclus
- 🔧 **Configuration :** `config.js`
- 📊 **Base de données :** Dashboard Supabase

**Bonne utilisation de CryptoBoost ! 🚀**
