<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - CryptoBoost</title>
    <meta name="description" content="Panneau d'administration CryptoBoost - Interface futuriste pour la gestion de la plateforme.">

    <!-- Font<PERSON> Futuristes -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700;800&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/cryptoboost-design-system.css">
    <link rel="stylesheet" href="assets/css/futuristic-dashboard.css">
    <link rel="stylesheet" href="assets/css/futuristic-admin.css">

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="config.js"></script>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body class="admin-body">
    <!-- Header Admin -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <div class="logo">
                    <h1>🚀 CryptoBoost Admin</h1>
                </div>
                <nav class="admin-menu">
                    <a href="#dashboard" class="nav-link active">Dashboard</a>
                    <a href="#users" class="nav-link">Utilisateurs</a>
                    <a href="#transactions" class="nav-link">Transactions</a>
                    <a href="#plans" class="nav-link">Plans</a>
                    <a href="#wallets" class="nav-link">Portefeuilles</a>
                    <a href="#settings" class="nav-link">Paramètres</a>
                </nav>
                <div class="admin-user">
                    <span id="adminName">Admin</span>
                    <button onclick="logout()" class="btn-logout">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <!-- Dashboard Section -->
            <section id="dashboard" class="admin-section active">
                <h2>📊 Tableau de Bord</h2>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">👥</div>
                        <div class="stat-content">
                            <h3 id="totalUsers">0</h3>
                            <p>Utilisateurs Total</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <div class="stat-content">
                            <h3 id="totalDeposits">0€</h3>
                            <p>Dépôts Total</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <div class="stat-content">
                            <h3 id="totalProfit">0€</h3>
                            <p>Profits Générés</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🏦</div>
                        <div class="stat-content">
                            <h3 id="totalWithdrawals">0€</h3>
                            <p>Retraits Total</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="admin-panel">
                    <h3>📋 Activité Récente</h3>
                    <div id="recentActivity" class="activity-list">
                        <div class="loading">Chargement...</div>
                    </div>
                </div>
            </section>

            <!-- Users Section -->
            <section id="users" class="admin-section">
                <h2>👥 Gestion des Utilisateurs</h2>
                
                <div class="admin-panel">
                    <div class="panel-header">
                        <h3>Liste des Utilisateurs</h3>
                        <button onclick="refreshUsers()" class="btn-refresh">🔄 Actualiser</button>
                    </div>
                    
                    <div class="table-container">
                        <table id="usersTable" class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Email</th>
                                    <th>Nom</th>
                                    <th>Solde</th>
                                    <th>Statut</th>
                                    <th>Inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr><td colspan="7" class="loading">Chargement des utilisateurs...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Transactions Section -->
            <section id="transactions" class="admin-section">
                <h2>💳 Gestion des Transactions</h2>
                
                <div class="admin-panel">
                    <div class="panel-header">
                        <h3>Transactions Récentes</h3>
                        <div class="filter-controls">
                            <select id="transactionFilter">
                                <option value="all">Toutes</option>
                                <option value="deposit">Dépôts</option>
                                <option value="withdrawal">Retraits</option>
                                <option value="profit">Profits</option>
                            </select>
                            <button onclick="refreshTransactions()" class="btn-refresh">🔄 Actualiser</button>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table id="transactionsTable" class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Utilisateur</th>
                                    <th>Type</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTableBody">
                                <tr><td colspan="7" class="loading">Chargement des transactions...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Plans Section -->
            <section id="plans" class="admin-section">
                <h2>📋 Gestion des Plans</h2>
                
                <div class="admin-panel">
                    <div class="panel-header">
                        <h3>Plans d'Investissement</h3>
                        <button onclick="addNewPlan()" class="btn-primary">➕ Nouveau Plan</button>
                    </div>
                    
                    <div id="plansGrid" class="plans-grid">
                        <div class="loading">Chargement des plans...</div>
                    </div>
                </div>
            </section>

            <!-- Wallets Section -->
            <section id="wallets" class="admin-section">
                <h2>🏦 Gestion des Portefeuilles</h2>
                
                <div class="admin-panel">
                    <div class="panel-header">
                        <h3>Portefeuilles de l'Entreprise</h3>
                        <button onclick="addNewWallet()" class="btn-primary">➕ Nouveau Portefeuille</button>
                    </div>
                    
                    <div id="walletsGrid" class="wallets-grid">
                        <div class="loading">Chargement des portefeuilles...</div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="admin-section">
                <h2>⚙️ Paramètres</h2>
                
                <div class="admin-panel">
                    <h3>Configuration de l'Application</h3>
                    
                    <form id="settingsForm" class="settings-form">
                        <div class="form-group">
                            <label for="siteName">Nom du Site</label>
                            <input type="text" id="siteName" name="site_name" value="CryptoBoost">
                        </div>
                        
                        <div class="form-group">
                            <label for="minDeposit">Dépôt Minimum (€)</label>
                            <input type="number" id="minDeposit" name="min_deposit" value="100">
                        </div>
                        
                        <div class="form-group">
                            <label for="maxDeposit">Dépôt Maximum (€)</label>
                            <input type="number" id="maxDeposit" name="max_deposit" value="100000">
                        </div>
                        
                        <div class="form-group">
                            <label for="withdrawalFee">Frais de Retrait (%)</label>
                            <input type="number" id="withdrawalFee" name="withdrawal_fee" value="2" step="0.1">
                        </div>
                        
                        <div class="form-group">
                            <label for="supportEmail">Email Support</label>
                            <input type="email" id="supportEmail" name="support_email" value="<EMAIL>">
                        </div>
                        
                        <button type="submit" class="btn-primary">💾 Sauvegarder</button>
                    </form>
                </div>
            </section>
        </div>
    </main>

    <!-- Scripts -->
    <script src="assets/js/admin.js"></script>
    <script>
        // Initialisation de l'admin
        document.addEventListener('DOMContentLoaded', function() {
            initializeAdmin();
            loadDashboardData();
        });

        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = this.getAttribute('href').substring(1);
                showSection(target);
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        function showSection(sectionId) {
            document.querySelectorAll('.admin-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(sectionId).classList.add('active');
        }

        function logout() {
            localStorage.removeItem('adminToken');
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>
