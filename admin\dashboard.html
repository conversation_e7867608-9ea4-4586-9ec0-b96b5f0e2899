<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - CryptoBoost</title>
    <link rel="stylesheet" href="../assets/css/modern-style.css">
    <link rel="stylesheet" href="../assets/css/admin-modern.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
</head>
<body class="admin-body">
    <!-- Header Admin -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <div class="logo">
                    <h1>🚀 CryptoBoost Admin</h1>
                </div>
                <nav class="admin-menu">
                    <a href="../admin.html" class="nav-link">🏠 Accueil</a>
                    <a href="users.html" class="nav-link">👥 Utilisateurs</a>
                    <a href="deposits.html" class="nav-link">💳 Dépôts</a>
                    <a href="withdrawals.html" class="nav-link">🏦 Retraits</a>
                    <a href="plans.html" class="nav-link">📋 Plans</a>
                    <a href="wallets.html" class="nav-link">🏦 Portefeuilles</a>
                </nav>
                <div class="admin-user">
                    <span id="adminName">Admin</span>
                    <button onclick="logout()" class="btn-logout">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="page-header">
                <h1>📊 Dashboard Administrateur</h1>
                <p>Vue d'ensemble de la plateforme CryptoBoost</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3 id="totalUsers">0</h3>
                        <p>Utilisateurs Total</p>
                        <small class="stat-change positive">+5 cette semaine</small>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <h3 id="totalDeposits">0€</h3>
                        <p>Dépôts Total</p>
                        <small class="stat-change positive">+12% ce mois</small>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">📈</div>
                    <div class="stat-content">
                        <h3 id="totalProfit">0€</h3>
                        <p>Profits Générés</p>
                        <small class="stat-change positive">+8% ce mois</small>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">🏦</div>
                    <div class="stat-content">
                        <h3 id="pendingWithdrawals">0</h3>
                        <p>Retraits en Attente</p>
                        <small class="stat-change neutral">À traiter</small>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="charts-row">
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>📈 Évolution des Dépôts</h3>
                        <select id="depositsPeriod">
                            <option value="7d">7 jours</option>
                            <option value="30d" selected>30 jours</option>
                            <option value="90d">90 jours</option>
                        </select>
                    </div>
                    <canvas id="depositsChart" width="400" height="200"></canvas>
                </div>
                
                <div class="chart-container">
                    <div class="chart-header">
                        <h3>🥧 Répartition des Plans</h3>
                    </div>
                    <canvas id="plansChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="admin-panel">
                <div class="panel-header">
                    <h3>📋 Activité Récente</h3>
                    <button onclick="refreshActivity()" class="btn-refresh">🔄 Actualiser</button>
                </div>
                <div id="recentActivity" class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon deposit">💳</div>
                        <div class="activity-content">
                            <p><strong>Nouveau dépôt</strong> - <EMAIL></p>
                            <small>500€ • Il y a 2 minutes</small>
                        </div>
                        <div class="activity-status pending">En attente</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon user">👤</div>
                        <div class="activity-content">
                            <p><strong>Nouvel utilisateur</strong> - <EMAIL></p>
                            <small>Inscription • Il y a 15 minutes</small>
                        </div>
                        <div class="activity-status success">Confirmé</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon withdrawal">🏦</div>
                        <div class="activity-content">
                            <p><strong>Demande de retrait</strong> - <EMAIL></p>
                            <small>1,200€ • Il y a 1 heure</small>
                        </div>
                        <div class="activity-status pending">À traiter</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>⚡ Actions Rapides</h3>
                <div class="actions-grid">
                    <a href="users.html" class="action-card">
                        <div class="action-icon">👥</div>
                        <h4>Gérer Utilisateurs</h4>
                        <p>Voir et gérer tous les utilisateurs</p>
                    </a>
                    <a href="deposits.html" class="action-card">
                        <div class="action-icon">💳</div>
                        <h4>Valider Dépôts</h4>
                        <p>Approuver les nouveaux dépôts</p>
                    </a>
                    <a href="withdrawals.html" class="action-card">
                        <div class="action-icon">🏦</div>
                        <h4>Traiter Retraits</h4>
                        <p>Gérer les demandes de retrait</p>
                    </a>
                    <a href="plans.html" class="action-card">
                        <div class="action-icon">📋</div>
                        <h4>Gérer Plans</h4>
                        <p>Modifier les plans d'investissement</p>
                    </a>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Initialisation du dashboard admin
        document.addEventListener('DOMContentLoaded', function() {
            loadAdminDashboard();
            loadStats();
            loadRecentActivity();
        });

        function logout() {
            localStorage.removeItem('adminToken');
            window.location.href = '../login.html';
        }

        function refreshActivity() {
            loadRecentActivity();
        }

        // Fonctions de chargement des données
        async function loadStats() {
            try {
                // Charger les statistiques depuis Supabase
                const stats = await getAdminStats();
                document.getElementById('totalUsers').textContent = stats.users || 0;
                document.getElementById('totalDeposits').textContent = (stats.deposits || 0) + '€';
                document.getElementById('totalProfit').textContent = (stats.profits || 0) + '€';
                document.getElementById('pendingWithdrawals').textContent = stats.pendingWithdrawals || 0;
            } catch (error) {
                console.error('Erreur lors du chargement des stats:', error);
            }
        }

        async function loadRecentActivity() {
            try {
                const activity = await getRecentActivity();
                displayActivity(activity);
            } catch (error) {
                console.error('Erreur lors du chargement de l\'activité:', error);
            }
        }

        function displayActivity(activities) {
            const container = document.getElementById('recentActivity');
            if (!activities || activities.length === 0) {
                container.innerHTML = '<p class="no-data">Aucune activité récente</p>';
                return;
            }

            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon ${activity.type}">${getActivityIcon(activity.type)}</div>
                    <div class="activity-content">
                        <p><strong>${activity.title}</strong> - ${activity.user}</p>
                        <small>${activity.details} • ${formatTime(activity.created_at)}</small>
                    </div>
                    <div class="activity-status ${activity.status}">${getStatusText(activity.status)}</div>
                </div>
            `).join('');
        }

        function getActivityIcon(type) {
            const icons = {
                'deposit': '💳',
                'withdrawal': '🏦',
                'user': '👤',
                'investment': '📊',
                'profit': '📈'
            };
            return icons[type] || '📋';
        }

        function getStatusText(status) {
            const texts = {
                'pending': 'En attente',
                'success': 'Confirmé',
                'approved': 'Approuvé',
                'rejected': 'Rejeté'
            };
            return texts[status] || status;
        }

        function formatTime(timestamp) {
            const now = new Date();
            const time = new Date(timestamp);
            const diff = now - time;
            
            if (diff < 60000) return 'À l\'instant';
            if (diff < 3600000) return `Il y a ${Math.floor(diff / 60000)} minutes`;
            if (diff < 86400000) return `Il y a ${Math.floor(diff / 3600000)} heures`;
            return `Il y a ${Math.floor(diff / 86400000)} jours`;
        }
    </script>
</body>
</html>
