<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - CryptoBoost</title>
    <meta name="description" content="Tableau de bord administrateur CryptoBoost - Gestion complète de la plateforme">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <link rel="stylesheet" href="../assets/css/cryptoboost-design-system.css">
    <link rel="stylesheet" href="../assets/css/modern-dashboard.css">
    <link rel="stylesheet" href="../assets/css/admin-modern.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body class="dashboard-body admin-dashboard">
    <!-- Sidebar -->
    <aside class="sidebar admin-sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <div class="brand-logo admin-logo">
                    <i class="fas fa-crown"></i>
                </div>
                <span class="brand-text">CryptoBoost Admin</span>
            </div>
            <button class="sidebar-toggle" id="sidebar-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-chart-pie"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="users.html" class="nav-link" data-page="users">
                        <i class="fas fa-users"></i>
                        <span>Utilisateurs</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="deposits.html" class="nav-link" data-page="deposits">
                        <i class="fas fa-arrow-down"></i>
                        <span>Dépôts</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="withdrawals.html" class="nav-link" data-page="withdrawals">
                        <i class="fas fa-arrow-up"></i>
                        <span>Retraits</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="plans.html" class="nav-link" data-page="plans">
                        <i class="fas fa-star"></i>
                        <span>Plans</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="wallets.html" class="nav-link" data-page="wallets">
                        <i class="fas fa-wallet"></i>
                        <span>Portefeuilles</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="notifications.html" class="nav-link" data-page="notifications">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="logs.html" class="nav-link" data-page="logs">
                        <i class="fas fa-file-alt"></i>
                        <span>Logs</span>
                    </a>
                </li>
            </ul>

            <div class="nav-divider"></div>

            <ul class="nav-list">
                <li class="nav-item">
                    <a href="../index-modern.html" class="nav-link">
                        <i class="fas fa-home"></i>
                        <span>Site Public</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" id="admin-settings">
                        <i class="fas fa-cog"></i>
                        <span>Paramètres</span>
                    </a>
                </li>
            </ul>
        </nav>

        <div class="sidebar-footer">
            <div class="user-profile admin-profile">
                <div class="user-avatar admin-avatar">
                    <i class="fas fa-user-shield"></i>
                </div>
                <div class="user-info">
                    <div class="user-name" id="adminName">Admin</div>
                    <div class="user-email"><EMAIL></div>
                </div>
                <button class="logout-btn" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Header -->
        <header class="dashboard-header admin-header">
            <div class="header-left">
                <button class="mobile-sidebar-toggle" id="mobile-sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title">Dashboard Administrateur</h1>
            </div>

            <div class="header-right">
                <div class="header-stats admin-stats">
                    <div class="stat-item">
                        <div class="stat-label">Utilisateurs Actifs</div>
                        <div class="stat-value" id="activeUsers">0</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">Volume 24h</div>
                        <div class="stat-value" id="dailyVolume">€0</div>
                    </div>
                </div>

                <div class="header-actions">
                    <button class="btn btn-outline btn-sm" id="refresh-btn">
                        <i class="fas fa-sync-alt"></i>
                        <span>Actualiser</span>
                    </button>
                    <button class="btn btn-warning btn-sm" id="add-user-btn">
                        <i class="fas fa-user-plus"></i>
                        <span>Ajouter Utilisateur</span>
                    </button>
                </div>

                <div class="notifications">
                    <button class="notification-btn" id="notification-btn">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationCount">0</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <div class="page-content admin-content" id="page-content">
            <!-- Admin Dashboard Grid -->
            <div class="admin-dashboard-grid">
                <!-- Key Metrics -->
                <div class="dashboard-card metrics-overview">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-bar"></i>
                            Métriques Clés
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-ghost btn-sm" id="export-metrics">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <div class="metric-icon users">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalUsers">0</div>
                                    <div class="metric-label">Utilisateurs Total</div>
                                    <div class="metric-change positive" id="usersChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon deposits">
                                    <i class="fas fa-arrow-down"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalDeposits">€0</div>
                                    <div class="metric-label">Dépôts Total</div>
                                    <div class="metric-change positive" id="depositsChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon withdrawals">
                                    <i class="fas fa-arrow-up"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalWithdrawals">€0</div>
                                    <div class="metric-label">Retraits Total</div>
                                    <div class="metric-change negative" id="withdrawalsChange">+0%</div>
                                </div>
                            </div>

                            <div class="metric-item">
                                <div class="metric-icon profit">
                                    <i class="fas fa-coins"></i>
                                </div>
                                <div class="metric-content">
                                    <div class="metric-value" id="totalProfit">€0</div>
                                    <div class="metric-label">Profit Total</div>
                                    <div class="metric-change positive" id="profitChange">+0%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-card recent-activity">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i>
                            Activité Récente
                        </h3>
                        <a href="logs.html" class="card-link">Voir tout</a>
                    </div>
                    <div class="card-body">
                        <div class="activity-list" id="recent-activity-list">
                            <!-- Activity items will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="dashboard-card system-status">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-server"></i>
                            État du Système
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-ghost btn-sm" id="refresh-status">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="status-grid">
                            <div class="status-item">
                                <div class="status-indicator online" id="api-status"></div>
                                <div class="status-content">
                                    <div class="status-label">API Supabase</div>
                                    <div class="status-value" id="api-status-text">En ligne</div>
                                </div>
                            </div>

                            <div class="status-item">
                                <div class="status-indicator online" id="db-status"></div>
                                <div class="status-content">
                                    <div class="status-label">Base de données</div>
                                    <div class="status-value" id="db-status-text">Opérationnelle</div>
                                </div>
                            </div>

                            <div class="status-item">
                                <div class="status-indicator warning" id="payment-status"></div>
                                <div class="status-content">
                                    <div class="status-label">Paiements</div>
                                    <div class="status-value" id="payment-status-text">Maintenance</div>
                                </div>
                            </div>

                            <div class="status-item">
                                <div class="status-indicator online" id="auth-status"></div>
                                <div class="status-content">
                                    <div class="status-label">Authentification</div>
                                    <div class="status-value" id="auth-status-text">Fonctionnelle</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card quick-actions">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-bolt"></i>
                            Actions Rapides
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="action-grid">
                            <button class="action-btn" data-action="add-user">
                                <div class="action-icon">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title">Ajouter Utilisateur</div>
                                    <div class="action-subtitle">Nouveau compte</div>
                                </div>
                            </button>

                            <button class="action-btn" data-action="approve-withdrawals">
                                <div class="action-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title">Approuver Retraits</div>
                                    <div class="action-subtitle">En attente</div>
                                </div>
                            </button>

                            <button class="action-btn" data-action="send-notification">
                                <div class="action-icon">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title">Notification</div>
                                    <div class="action-subtitle">Envoyer message</div>
                                </div>
                            </button>

                            <button class="action-btn" data-action="backup-data">
                                <div class="action-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="action-text">
                                    <div class="action-title">Sauvegarde</div>
                                    <div class="action-subtitle">Backup données</div>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Top Users -->
                <div class="dashboard-card top-users">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-trophy"></i>
                            Top Utilisateurs
                        </h3>
                        <a href="users.html" class="card-link">Voir tout</a>
                    </div>
                    <div class="card-body">
                        <div class="users-list" id="top-users-list">
                            <!-- Top users will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="../config.js"></script>
    <script src="../assets/js/supabase-api.js"></script>
    <script src="../assets/js/modern-dashboard.js"></script>
    <script src="../assets/js/admin-dashboard.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>