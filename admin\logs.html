<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs Système - CryptoBoost Admin</title>
    <link rel="stylesheet" href="../assets/css/modern-style.css">
    <link rel="stylesheet" href="../assets/css/admin-modern.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
</head>
<body class="admin-body">
    <!-- Header Admin -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <div class="logo">
                    <h1>🚀 CryptoBoost Admin</h1>
                </div>
                <nav class="admin-menu">
                    <a href="../admin.html" class="nav-link">🏠 Accueil</a>
                    <a href="dashboard.html" class="nav-link">📊 Dashboard</a>
                    <a href="users.html" class="nav-link">👥 Utilisateurs</a>
                    <a href="deposits.html" class="nav-link">💳 Dépôts</a>
                    <a href="withdrawals.html" class="nav-link">🏦 Retraits</a>
                    <a href="plans.html" class="nav-link">📋 Plans</a>
                    <a href="wallets.html" class="nav-link">🏦 Portefeuilles</a>
                    <a href="logs.html" class="nav-link active">📝 Logs</a>
                    <a href="notifications.html" class="nav-link">🔔 Notifications</a>
                </nav>
                <div class="admin-user">
                    <span id="adminName">Admin</span>
                    <button onclick="logout()" class="btn-logout">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="page-header">
                <h1>📝 Logs Système</h1>
                <p>Historique des activités et événements de la plateforme</p>
            </div>

            <!-- Filters -->
            <div class="filters-panel">
                <div class="filter-group">
                    <label for="logLevel">Niveau</label>
                    <select id="logLevel">
                        <option value="all">Tous les niveaux</option>
                        <option value="info">Info</option>
                        <option value="warning">Avertissement</option>
                        <option value="error">Erreur</option>
                        <option value="critical">Critique</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="logCategory">Catégorie</label>
                    <select id="logCategory">
                        <option value="all">Toutes les catégories</option>
                        <option value="auth">Authentification</option>
                        <option value="transaction">Transactions</option>
                        <option value="user">Utilisateurs</option>
                        <option value="system">Système</option>
                        <option value="security">Sécurité</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="dateRange">Période</label>
                    <select id="dateRange">
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month" selected>Ce mois</option>
                        <option value="all">Tout l'historique</option>
                    </select>
                </div>
                
                <div class="filter-actions">
                    <button onclick="applyFilters()" class="btn-primary">🔍 Filtrer</button>
                    <button onclick="refreshLogs()" class="btn-secondary">🔄 Actualiser</button>
                    <button onclick="exportLogs()" class="btn-secondary">📥 Exporter</button>
                </div>
            </div>

            <!-- Stats Summary -->
            <div class="logs-stats">
                <div class="stat-item">
                    <span class="stat-icon info">ℹ️</span>
                    <div class="stat-content">
                        <h3 id="infoCount">0</h3>
                        <p>Informations</p>
                    </div>
                </div>
                <div class="stat-item">
                    <span class="stat-icon warning">⚠️</span>
                    <div class="stat-content">
                        <h3 id="warningCount">0</h3>
                        <p>Avertissements</p>
                    </div>
                </div>
                <div class="stat-item">
                    <span class="stat-icon error">❌</span>
                    <div class="stat-content">
                        <h3 id="errorCount">0</h3>
                        <p>Erreurs</p>
                    </div>
                </div>
                <div class="stat-item">
                    <span class="stat-icon critical">🚨</span>
                    <div class="stat-content">
                        <h3 id="criticalCount">0</h3>
                        <p>Critiques</p>
                    </div>
                </div>
            </div>

            <!-- Logs Table -->
            <div class="admin-panel">
                <div class="panel-header">
                    <h3>📋 Historique des Logs</h3>
                    <div class="panel-actions">
                        <input type="text" id="searchLogs" placeholder="Rechercher dans les logs..." class="search-input">
                        <button onclick="clearLogs()" class="btn-danger">🗑️ Vider les logs</button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Niveau</th>
                                <th>Catégorie</th>
                                <th>Message</th>
                                <th>Utilisateur</th>
                                <th>IP</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <tr>
                                <td colspan="7" class="loading">Chargement des logs...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="pagination">
                    <button onclick="previousPage()" id="prevBtn" class="btn-pagination" disabled>← Précédent</button>
                    <span id="pageInfo">Page 1 sur 1</span>
                    <button onclick="nextPage()" id="nextBtn" class="btn-pagination" disabled>Suivant →</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Log Details Modal -->
    <div id="logDetailsModal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h3>📝 Détails du Log</h3>
                <span class="close" onclick="closeModal('logDetailsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="logDetailsContent">
                    <!-- Log details will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/admin.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let currentFilters = {
            level: 'all',
            category: 'all',
            dateRange: 'month',
            search: ''
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
            loadLogStats();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchLogs').addEventListener('input', function(e) {
                currentFilters.search = e.target.value;
                debounceSearch();
            });
        }

        let searchTimeout;
        function debounceSearch() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                currentPage = 1;
                loadLogs();
            }, 500);
        }

        async function loadLogs() {
            try {
                const logs = await getSystemLogs(currentFilters, currentPage);
                displayLogs(logs.data);
                updatePagination(logs.pagination);
            } catch (error) {
                console.error('Erreur lors du chargement des logs:', error);
                showError('Erreur lors du chargement des logs');
            }
        }

        async function loadLogStats() {
            try {
                const stats = await getLogStats(currentFilters.dateRange);
                document.getElementById('infoCount').textContent = stats.info || 0;
                document.getElementById('warningCount').textContent = stats.warning || 0;
                document.getElementById('errorCount').textContent = stats.error || 0;
                document.getElementById('criticalCount').textContent = stats.critical || 0;
            } catch (error) {
                console.error('Erreur lors du chargement des stats:', error);
            }
        }

        function displayLogs(logs) {
            const tbody = document.getElementById('logsTableBody');
            
            if (!logs || logs.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="no-data">Aucun log trouvé</td></tr>';
                return;
            }

            tbody.innerHTML = logs.map(log => `
                <tr class="log-row ${log.level}">
                    <td class="timestamp">${formatDateTime(log.created_at)}</td>
                    <td><span class="log-level ${log.level}">${getLevelIcon(log.level)} ${log.level.toUpperCase()}</span></td>
                    <td><span class="log-category">${log.category}</span></td>
                    <td class="log-message">${truncateMessage(log.message)}</td>
                    <td>${log.user_email || 'Système'}</td>
                    <td class="ip-address">${log.ip_address || '-'}</td>
                    <td>
                        <button onclick="viewLogDetails('${log.id}')" class="btn-action">👁️</button>
                    </td>
                </tr>
            `).join('');
        }

        function getLevelIcon(level) {
            const icons = {
                'info': 'ℹ️',
                'warning': '⚠️',
                'error': '❌',
                'critical': '🚨'
            };
            return icons[level] || 'ℹ️';
        }

        function truncateMessage(message, maxLength = 100) {
            if (message.length <= maxLength) return message;
            return message.substring(0, maxLength) + '...';
        }

        function formatDateTime(timestamp) {
            return new Date(timestamp).toLocaleString('fr-FR');
        }

        function applyFilters() {
            currentFilters.level = document.getElementById('logLevel').value;
            currentFilters.category = document.getElementById('logCategory').value;
            currentFilters.dateRange = document.getElementById('dateRange').value;
            currentPage = 1;
            loadLogs();
            loadLogStats();
        }

        function refreshLogs() {
            loadLogs();
            loadLogStats();
        }

        function exportLogs() {
            // Implement log export functionality
            alert('Fonctionnalité d\'export en cours de développement');
        }

        function clearLogs() {
            if (confirm('Êtes-vous sûr de vouloir supprimer tous les logs ? Cette action est irréversible.')) {
                // Implement clear logs functionality
                alert('Fonctionnalité de suppression en cours de développement');
            }
        }

        function viewLogDetails(logId) {
            // Load and display detailed log information
            document.getElementById('logDetailsModal').style.display = 'block';
            loadLogDetails(logId);
        }

        async function loadLogDetails(logId) {
            try {
                const log = await getLogDetails(logId);
                displayLogDetails(log);
            } catch (error) {
                console.error('Erreur lors du chargement des détails:', error);
            }
        }

        function displayLogDetails(log) {
            const content = document.getElementById('logDetailsContent');
            content.innerHTML = `
                <div class="log-details">
                    <div class="detail-row">
                        <label>ID:</label>
                        <span>${log.id}</span>
                    </div>
                    <div class="detail-row">
                        <label>Timestamp:</label>
                        <span>${formatDateTime(log.created_at)}</span>
                    </div>
                    <div class="detail-row">
                        <label>Niveau:</label>
                        <span class="log-level ${log.level}">${getLevelIcon(log.level)} ${log.level.toUpperCase()}</span>
                    </div>
                    <div class="detail-row">
                        <label>Catégorie:</label>
                        <span>${log.category}</span>
                    </div>
                    <div class="detail-row">
                        <label>Utilisateur:</label>
                        <span>${log.user_email || 'Système'}</span>
                    </div>
                    <div class="detail-row">
                        <label>Adresse IP:</label>
                        <span>${log.ip_address || '-'}</span>
                    </div>
                    <div class="detail-row full-width">
                        <label>Message:</label>
                        <div class="log-message-full">${log.message}</div>
                    </div>
                    ${log.stack_trace ? `
                        <div class="detail-row full-width">
                            <label>Stack Trace:</label>
                            <pre class="stack-trace">${log.stack_trace}</pre>
                        </div>
                    ` : ''}
                    ${log.metadata ? `
                        <div class="detail-row full-width">
                            <label>Métadonnées:</label>
                            <pre class="metadata">${JSON.stringify(log.metadata, null, 2)}</pre>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        function updatePagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;
            
            document.getElementById('pageInfo').textContent = `Page ${currentPage} sur ${totalPages}`;
            document.getElementById('prevBtn').disabled = currentPage <= 1;
            document.getElementById('nextBtn').disabled = currentPage >= totalPages;
        }

        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                loadLogs();
            }
        }

        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadLogs();
            }
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function logout() {
            localStorage.removeItem('adminToken');
            window.location.href = '../login.html';
        }

        function showError(message) {
            // Simple error display - could be enhanced with a proper notification system
            alert('Erreur: ' + message);
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
