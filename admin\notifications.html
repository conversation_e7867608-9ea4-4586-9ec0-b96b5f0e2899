<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - CryptoBoost Admin</title>
    <link rel="stylesheet" href="../assets/css/modern-style.css">
    <link rel="stylesheet" href="../assets/css/admin-modern.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../config.js"></script>
</head>
<body class="admin-body">
    <!-- Header Admin -->
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <div class="logo">
                    <h1>🚀 CryptoBoost Admin</h1>
                </div>
                <nav class="admin-menu">
                    <a href="../admin.html" class="nav-link">🏠 Accueil</a>
                    <a href="dashboard.html" class="nav-link">📊 Dashboard</a>
                    <a href="users.html" class="nav-link">👥 Utilisateurs</a>
                    <a href="deposits.html" class="nav-link">💳 Dépôts</a>
                    <a href="withdrawals.html" class="nav-link">🏦 Retraits</a>
                    <a href="plans.html" class="nav-link">📋 Plans</a>
                    <a href="wallets.html" class="nav-link">🏦 Portefeuilles</a>
                    <a href="logs.html" class="nav-link">📝 Logs</a>
                    <a href="notifications.html" class="nav-link active">🔔 Notifications</a>
                </nav>
                <div class="admin-user">
                    <span id="adminName">Admin</span>
                    <button onclick="logout()" class="btn-logout">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="admin-main">
        <div class="container">
            <div class="page-header">
                <h1>🔔 Gestion des Notifications</h1>
                <p>Envoyer et gérer les notifications aux utilisateurs</p>
            </div>

            <!-- Quick Stats -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">📨</div>
                    <div class="stat-content">
                        <h3 id="totalSent">0</h3>
                        <p>Envoyées aujourd'hui</p>
                    </div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <h3 id="activeUsers">0</h3>
                        <p>Utilisateurs actifs</p>
                    </div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">⏰</div>
                    <div class="stat-content">
                        <h3 id="pendingNotifications">0</h3>
                        <p>En attente</p>
                    </div>
                </div>
                <div class="stat-card info">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <h3 id="openRate">0%</h3>
                        <p>Taux d'ouverture</p>
                    </div>
                </div>
            </div>

            <!-- Send Notification Panel -->
            <div class="admin-panel">
                <div class="panel-header">
                    <h3>📤 Nouvelle Notification</h3>
                </div>
                <div class="panel-body">
                    <form id="notificationForm" class="notification-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="notificationType">Type de notification</label>
                                <select id="notificationType" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="info">Information</option>
                                    <option value="success">Succès</option>
                                    <option value="warning">Avertissement</option>
                                    <option value="error">Erreur</option>
                                    <option value="promotion">Promotion</option>
                                    <option value="maintenance">Maintenance</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="priority">Priorité</label>
                                <select id="priority" required>
                                    <option value="low">Basse</option>
                                    <option value="normal" selected>Normale</option>
                                    <option value="high">Haute</option>
                                    <option value="urgent">Urgente</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="recipients">Destinataires</label>
                                <select id="recipients" required>
                                    <option value="all">Tous les utilisateurs</option>
                                    <option value="active">Utilisateurs actifs</option>
                                    <option value="inactive">Utilisateurs inactifs</option>
                                    <option value="premium">Utilisateurs premium</option>
                                    <option value="new">Nouveaux utilisateurs</option>
                                    <option value="custom">Sélection personnalisée</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="scheduledTime">Programmer (optionnel)</label>
                                <input type="datetime-local" id="scheduledTime">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="title">Titre</label>
                            <input type="text" id="title" maxlength="100" required>
                            <small class="char-count">0/100 caractères</small>
                        </div>

                        <div class="form-group">
                            <label for="message">Message</label>
                            <textarea id="message" rows="5" maxlength="500" required></textarea>
                            <small class="char-count">0/500 caractères</small>
                        </div>

                        <div class="form-group" id="customRecipientsGroup" style="display: none;">
                            <label for="customRecipients">Emails personnalisés (séparés par des virgules)</label>
                            <textarea id="customRecipients" rows="3" placeholder="<EMAIL>, <EMAIL>"></textarea>
                        </div>

                        <div class="form-actions">
                            <button type="button" onclick="previewNotification()" class="btn-secondary">👁️ Aperçu</button>
                            <button type="submit" class="btn-primary">📤 Envoyer maintenant</button>
                            <button type="button" onclick="saveAsDraft()" class="btn-outline">💾 Sauvegarder comme brouillon</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Templates Panel -->
            <div class="admin-panel">
                <div class="panel-header">
                    <h3>📝 Modèles de Notifications</h3>
                    <button onclick="createTemplate()" class="btn-primary">➕ Nouveau modèle</button>
                </div>
                <div class="templates-grid" id="templatesGrid">
                    <div class="template-card">
                        <div class="template-header">
                            <h4>Bienvenue</h4>
                            <span class="template-type info">Info</span>
                        </div>
                        <p>Message de bienvenue pour les nouveaux utilisateurs</p>
                        <div class="template-actions">
                            <button onclick="useTemplate('welcome')" class="btn-sm">📝 Utiliser</button>
                            <button onclick="editTemplate('welcome')" class="btn-sm">✏️ Éditer</button>
                        </div>
                    </div>
                    
                    <div class="template-card">
                        <div class="template-header">
                            <h4>Maintenance</h4>
                            <span class="template-type warning">Avertissement</span>
                        </div>
                        <p>Notification de maintenance programmée</p>
                        <div class="template-actions">
                            <button onclick="useTemplate('maintenance')" class="btn-sm">📝 Utiliser</button>
                            <button onclick="editTemplate('maintenance')" class="btn-sm">✏️ Éditer</button>
                        </div>
                    </div>
                    
                    <div class="template-card">
                        <div class="template-header">
                            <h4>Promotion</h4>
                            <span class="template-type success">Promotion</span>
                        </div>
                        <p>Offre spéciale et promotions</p>
                        <div class="template-actions">
                            <button onclick="useTemplate('promotion')" class="btn-sm">📝 Utiliser</button>
                            <button onclick="editTemplate('promotion')" class="btn-sm">✏️ Éditer</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Notifications -->
            <div class="admin-panel">
                <div class="panel-header">
                    <h3>📋 Notifications Récentes</h3>
                    <div class="panel-actions">
                        <select id="statusFilter">
                            <option value="all">Tous les statuts</option>
                            <option value="sent">Envoyées</option>
                            <option value="scheduled">Programmées</option>
                            <option value="draft">Brouillons</option>
                            <option value="failed">Echec</option>
                        </select>
                        <button onclick="refreshNotifications()" class="btn-refresh">🔄</button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Titre</th>
                                <th>Type</th>
                                <th>Destinataires</th>
                                <th>Statut</th>
                                <th>Taux d'ouverture</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="notificationsTableBody">
                            <tr>
                                <td colspan="7" class="loading">Chargement des notifications...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Preview Modal -->
    <div id="previewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>👁️ Aperçu de la Notification</h3>
                <span class="close" onclick="closeModal('previewModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="notification-preview">
                    <div class="preview-device">
                        <div class="device-header">Notification CryptoBoost</div>
                        <div class="notification-content">
                            <div class="notification-icon" id="previewIcon">🔔</div>
                            <div class="notification-text">
                                <h4 id="previewTitle">Titre de la notification</h4>
                                <p id="previewMessage">Message de la notification...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Modal -->
    <div id="templateModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="templateModalTitle">📝 Nouveau Modèle</h3>
                <span class="close" onclick="closeModal('templateModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="templateForm">
                    <div class="form-group">
                        <label for="templateName">Nom du modèle</label>
                        <input type="text" id="templateName" required>
                    </div>
                    <div class="form-group">
                        <label for="templateType">Type</label>
                        <select id="templateType" required>
                            <option value="info">Information</option>
                            <option value="success">Succès</option>
                            <option value="warning">Avertissement</option>
                            <option value="error">Erreur</option>
                            <option value="promotion">Promotion</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="templateTitle">Titre</label>
                        <input type="text" id="templateTitle" required>
                    </div>
                    <div class="form-group">
                        <label for="templateMessage">Message</label>
                        <textarea id="templateMessage" rows="4" required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-primary">💾 Sauvegarder</button>
                        <button type="button" onclick="closeModal('templateModal')" class="btn-secondary">Annuler</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/admin.js"></script>
    <script>
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadNotificationStats();
            loadRecentNotifications();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Character counters
            document.getElementById('title').addEventListener('input', updateCharCount);
            document.getElementById('message').addEventListener('input', updateCharCount);
            
            // Recipients change
            document.getElementById('recipients').addEventListener('change', function() {
                const customGroup = document.getElementById('customRecipientsGroup');
                customGroup.style.display = this.value === 'custom' ? 'block' : 'none';
            });
            
            // Form submission
            document.getElementById('notificationForm').addEventListener('submit', sendNotification);
            document.getElementById('templateForm').addEventListener('submit', saveTemplate);
        }

        function updateCharCount(e) {
            const input = e.target;
            const maxLength = input.getAttribute('maxlength');
            const currentLength = input.value.length;
            const counter = input.parentNode.querySelector('.char-count');
            counter.textContent = `${currentLength}/${maxLength} caractères`;
            
            if (currentLength > maxLength * 0.9) {
                counter.style.color = '#f59e0b';
            } else {
                counter.style.color = '';
            }
        }

        async function loadNotificationStats() {
            try {
                const stats = await getNotificationStats();
                document.getElementById('totalSent').textContent = stats.totalSent || 0;
                document.getElementById('activeUsers').textContent = stats.activeUsers || 0;
                document.getElementById('pendingNotifications').textContent = stats.pending || 0;
                document.getElementById('openRate').textContent = (stats.openRate || 0) + '%';
            } catch (error) {
                console.error('Erreur lors du chargement des stats:', error);
            }
        }

        async function loadRecentNotifications() {
            try {
                const notifications = await getRecentNotifications();
                displayNotifications(notifications);
            } catch (error) {
                console.error('Erreur lors du chargement des notifications:', error);
            }
        }

        function displayNotifications(notifications) {
            const tbody = document.getElementById('notificationsTableBody');
            
            if (!notifications || notifications.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="no-data">Aucune notification</td></tr>';
                return;
            }

            tbody.innerHTML = notifications.map(notif => `
                <tr>
                    <td>${formatDateTime(notif.created_at)}</td>
                    <td>${notif.title}</td>
                    <td><span class="notification-type ${notif.type}">${getTypeIcon(notif.type)} ${notif.type}</span></td>
                    <td>${notif.recipients_count} utilisateurs</td>
                    <td><span class="status ${notif.status}">${getStatusText(notif.status)}</span></td>
                    <td>${notif.open_rate || 0}%</td>
                    <td>
                        <button onclick="viewNotification('${notif.id}')" class="btn-action">👁️</button>
                        ${notif.status === 'draft' ? `<button onclick="editNotification('${notif.id}')" class="btn-action">✏️</button>` : ''}
                        <button onclick="duplicateNotification('${notif.id}')" class="btn-action">📋</button>
                    </td>
                </tr>
            `).join('');
        }

        function getTypeIcon(type) {
            const icons = {
                'info': 'ℹ️',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌',
                'promotion': '🎉',
                'maintenance': '🔧'
            };
            return icons[type] || '🔔';
        }

        function getStatusText(status) {
            const texts = {
                'sent': 'Envoyée',
                'scheduled': 'Programmée',
                'draft': 'Brouillon',
                'failed': 'Échec'
            };
            return texts[status] || status;
        }

        function previewNotification() {
            const title = document.getElementById('title').value;
            const message = document.getElementById('message').value;
            const type = document.getElementById('notificationType').value;
            
            if (!title || !message) {
                alert('Veuillez remplir le titre et le message');
                return;
            }
            
            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewMessage').textContent = message;
            document.getElementById('previewIcon').textContent = getTypeIcon(type);
            
            document.getElementById('previewModal').style.display = 'block';
        }

        async function sendNotification(e) {
            e.preventDefault();
            
            const formData = {
                type: document.getElementById('notificationType').value,
                priority: document.getElementById('priority').value,
                recipients: document.getElementById('recipients').value,
                title: document.getElementById('title').value,
                message: document.getElementById('message').value,
                scheduledTime: document.getElementById('scheduledTime').value,
                customRecipients: document.getElementById('customRecipients').value
            };
            
            try {
                await createNotification(formData);
                alert('Notification envoyée avec succès!');
                document.getElementById('notificationForm').reset();
                loadRecentNotifications();
                loadNotificationStats();
            } catch (error) {
                alert('Erreur lors de l\'envoi: ' + error.message);
            }
        }

        function saveAsDraft() {
            // Implement save as draft functionality
            alert('Fonctionnalité de brouillon en cours de développement');
        }

        function useTemplate(templateId) {
            // Load template data and fill form
            const templates = {
                'welcome': {
                    type: 'info',
                    title: 'Bienvenue sur CryptoBoost!',
                    message: 'Félicitations! Votre compte a été créé avec succès. Découvrez nos plans d\'investissement et commencez à faire fructifier votre capital.'
                },
                'maintenance': {
                    type: 'warning',
                    title: 'Maintenance programmée',
                    message: 'Notre plateforme sera en maintenance le [DATE] de [HEURE] à [HEURE]. Pendant cette période, l\'accès pourrait être temporairement indisponible.'
                },
                'promotion': {
                    type: 'promotion',
                    title: 'Offre spéciale limitée!',
                    message: 'Profitez de notre offre exceptionnelle: [DETAILS]. Cette promotion est valable jusqu\'au [DATE]. Ne manquez pas cette opportunité!'
                }
            };
            
            const template = templates[templateId];
            if (template) {
                document.getElementById('notificationType').value = template.type;
                document.getElementById('title').value = template.title;
                document.getElementById('message').value = template.message;
                updateCharCount({ target: document.getElementById('title') });
                updateCharCount({ target: document.getElementById('message') });
            }
        }

        function createTemplate() {
            document.getElementById('templateModalTitle').textContent = '📝 Nouveau Modèle';
            document.getElementById('templateForm').reset();
            document.getElementById('templateModal').style.display = 'block';
        }

        function editTemplate(templateId) {
            document.getElementById('templateModalTitle').textContent = '✏️ Éditer le Modèle';
            // Load template data for editing
            document.getElementById('templateModal').style.display = 'block';
        }

        function saveTemplate(e) {
            e.preventDefault();
            // Implement template saving
            alert('Modèle sauvegardé avec succès!');
            closeModal('templateModal');
        }

        function refreshNotifications() {
            loadRecentNotifications();
        }

        function viewNotification(id) {
            // Implement notification viewing
            alert('Affichage des détails de la notification: ' + id);
        }

        function editNotification(id) {
            // Implement notification editing
            alert('Edition de la notification: ' + id);
        }

        function duplicateNotification(id) {
            // Implement notification duplication
            alert('Duplication de la notification: ' + id);
        }

        function formatDateTime(timestamp) {
            return new Date(timestamp).toLocaleString('fr-FR');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function logout() {
            localStorage.removeItem('adminToken');
            window.location.href = '../login.html';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
