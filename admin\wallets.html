<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Wallets - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/modern-style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <nav class="sidebar glass-card">
            <div class="sidebar-header">
                <h2>CryptoBoost</h2>
                <span>Admin Panel</span>
            </div>
            <ul>
                <li><a href="index.html">Dashboard</a></li>
                <li><a href="users.html">Users</a></li>
                <li><a href="deposits.html">Deposits</a></li>
                <li><a href="withdrawals.html">Withdrawals</a></li>
                <li><a href="plans.html">Manage Plans</a></li>
                <li><a href="wallets.html" class="active">Manage Wallets</a></li>
                <li><a href="#" id="logout-btn">Logout</a></li>
            </ul>
        </nav>

        <main class="page-content">
            <div class="glass-card">
                <h1>Manage Wallets</h1>
                <p>Add, edit, or remove deposit wallet addresses.</p>
                <div class="table-container">
                    <table id="admin-table">
                        <thead>
                            <tr>
                                <th>Crypto Name</th>
                                <th>Wallet Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="wallets-table-body">
                            <!-- Wallet rows will be dynamically inserted here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="glass-card" style="margin-top: 2rem;">
                <h2 id="wallet-form-title">Add New Wallet</h2>
                <form id="wallet-form" class="admin-form">
                    <input type="hidden" id="wallet-original-name" name="original_name">
                    <div class="form-group">
                        <label for="wallet-name">Crypto Name (e.g., Bitcoin)</label>
                        <input type="text" id="wallet-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="wallet-address">Wallet Address</label>
                        <input type="text" id="wallet-address" name="value" required>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn-approve">Save Wallet</button>
                        <button type="button" id="clear-form-btn" class="btn-reject">Clear</button>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="../config.js"></script>
    <script src="../assets/js/api.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
