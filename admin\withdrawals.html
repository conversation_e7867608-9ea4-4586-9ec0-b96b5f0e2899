<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Withdrawals - Admin Panel</title>
    <link rel="stylesheet" href="../assets/css/modern-style.css">
    <link rel="stylesheet" href="../assets/css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <nav class="sidebar glass-card">
            <div class="sidebar-header">
                <h2>CryptoBoost</h2>
                <span>Admin Panel</span>
            </div>
            <ul>
                <li><a href="index.html">Dashboard</a></li>
                <li><a href="users.html">Users</a></li>
                <li><a href="deposits.html">Deposits</a></li>
                <li><a href="withdrawals.html" class="active">Withdrawals</a></li>
                <li><a href="plans.html">Manage Plans</a></li>
                <li><a href="wallets.html">Manage Wallets</a></li>
                <li><a href="#" id="logout-btn">Logout</a></li>
            </ul>
        </nav>

        <main class="page-content glass-card">
            <h1>Manage Withdrawals</h1>
            <p>Review and approve or reject pending withdrawal requests.</p>
            <div class="table-container">
                <table id="admin-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>User Email</th>
                            <th>Crypto</th>
                            <th>Amount</th>
                            <th>Wallet Address</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Withdrawal rows will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>

    <script src="../config.js"></script>
    <script src="../assets/js/api.js"></script>
    <script src="../assets/js/auth.js"></script>
    <script src="../assets/js/admin.js"></script>
</body>
</html>
