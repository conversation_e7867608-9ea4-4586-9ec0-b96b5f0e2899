<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 CryptoBoost API Complete Test</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4f46e5;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 10px 5px;
            font-size: 14px;
        }
        button:hover { background: #4338ca; }
        .score-display {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            margin: 20px 0;
        }
        .score-number {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        pre {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
            max-height: 200px;
        }
        .api-endpoint {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .endpoint-url {
            font-family: monospace;
            background: #1f2937;
            color: #10b981;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 CryptoBoost API Complete Test</h1>
        <p>Comprehensive API functionality test and validation system.</p>
        
        <div class="score-display">
            <div class="score-number" id="overallScore">0%</div>
            <div>API Functionality Score</div>
        </div>

        <button onclick="runCompleteTest()">🚀 Run Complete Test</button>
        <button onclick="testGlobalObjects()">🌐 Test Global Objects</button>
        <button onclick="testAllEndpoints()">🧪 Test All Endpoints</button>
        <button onclick="runFinalValidation()">✅ Final Validation</button>
        <button onclick="clearResults()">🧹 Clear Results</button>

        <div id="results"></div>
    </div>

    <!-- Load scripts in correct order -->
    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        let testResults = { total: 0, passed: 0, failed: 0 };

        function addResult(title, content, type = 'info') {
            const div = document.createElement('div');
            div.className = 'test-section';
            div.innerHTML = `
                <h3>${title}</h3>
                <div class="result ${type}">${content}</div>
            `;
            document.getElementById('results').appendChild(div);
            
            testResults.total++;
            if (type === 'success') testResults.passed++;
            else if (type === 'error') testResults.failed++;
            
            updateScore();
        }

        function updateScore() {
            const score = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('overallScore').textContent = `${score}%`;
            
            const scoreElement = document.querySelector('.score-display');
            if (score >= 90) {
                scoreElement.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            } else if (score >= 70) {
                scoreElement.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
            } else {
                scoreElement.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = { total: 0, passed: 0, failed: 0 };
            updateScore();
        }

        async function testGlobalObjects() {
            addResult('🌐 Testing Global Objects', 'Checking availability of global objects...', 'info');

            const requiredObjects = [
                { name: 'supabaseConfig', description: 'Supabase configuration' },
                { name: 'apiEndpoints', description: 'API endpoints mapping' },
                { name: 'tables', description: 'Table names mapping' },
                { name: 'supabaseAPI', description: 'Supabase API instance' }
            ];

            for (const obj of requiredObjects) {
                if (typeof window[obj.name] !== 'undefined') {
                    addResult(`✅ ${obj.name}`, 
                        `<p><strong>Status:</strong> Available</p>
                         <p><strong>Type:</strong> ${typeof window[obj.name]}</p>
                         <p><strong>Description:</strong> ${obj.description}</p>`, 
                        'success');
                } else {
                    addResult(`❌ ${obj.name}`, 
                        `<p><strong>Status:</strong> Missing</p>
                         <p><strong>Description:</strong> ${obj.description}</p>`, 
                        'error');
                }
            }
        }

        async function testAllEndpoints() {
            if (!window.supabaseAPI) {
                addResult('❌ API Not Available', 'supabaseAPI is not available. Check global objects first.', 'error');
                return;
            }

            addResult('🧪 Testing All Endpoints', 'Validating all API endpoints...', 'info');

            const endpoints = [
                { name: 'Users', method: 'get', args: ['users'] },
                { name: 'Investment Plans', method: 'getInvestmentPlans', args: [] },
                { name: 'Company Wallets', method: 'getCompanyWallets', args: [] },
                { name: 'Configuration', method: 'getConfig', args: [] },
                { name: 'User Transactions', method: 'getUserTransactions', args: ['<EMAIL>'] },
                { name: 'User by Email', method: 'getUserByEmail', args: ['<EMAIL>'] }
            ];

            for (const endpoint of endpoints) {
                try {
                    const startTime = Date.now();
                    const result = await window.supabaseAPI[endpoint.method](...endpoint.args);
                    const endTime = Date.now();
                    const duration = endTime - startTime;
                    
                    const count = Array.isArray(result) ? result.length : 'N/A';
                    addResult(`✅ ${endpoint.name}`, 
                        `<div class="api-endpoint">
                            <strong>Method:</strong> ${endpoint.method}<br>
                            <strong>Result:</strong> ${count} records<br>
                            <strong>Duration:</strong> ${duration}ms<br>
                            <strong>Status:</strong> Success
                        </div>`, 
                        'success');
                } catch (error) {
                    addResult(`❌ ${endpoint.name}`, 
                        `<div class="api-endpoint">
                            <strong>Method:</strong> ${endpoint.method}<br>
                            <strong>Error:</strong> ${error.message}<br>
                            <strong>Status:</strong> Failed
                        </div>`, 
                        'error');
                }
            }
        }

        async function runFinalValidation() {
            addResult('✅ Final Validation', 'Running comprehensive validation...', 'info');

            // Check global objects
            const globalObjects = ['supabaseConfig', 'apiEndpoints', 'supabaseAPI'];
            const available = globalObjects.filter(obj => typeof window[obj] !== 'undefined');
            const missing = globalObjects.filter(obj => typeof window[obj] === 'undefined');

            if (missing.length === 0) {
                addResult('✅ Global Objects', 'All required global objects are available', 'success');
            } else {
                addResult('❌ Global Objects', `Missing: ${missing.join(', ')}`, 'error');
            }

            // Test API functionality
            if (window.supabaseAPI) {
                try {
                    const testResult = await window.supabaseAPI.get('users');
                    addResult('✅ API Functionality', `API is working correctly - ${testResult.length} users found`, 'success');
                } catch (error) {
                    addResult('❌ API Functionality', `API error: ${error.message}`, 'error');
                }
            }

            // Calculate final score
            const finalScore = Math.round((testResults.passed / testResults.total) * 100);
            if (finalScore >= 90) {
                addResult('🎉 PRODUCTION READY', 
                    `<div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 32px; font-weight: bold;">${finalScore}%</div>
                        <div>CryptoBoost API is ready for production!</div>
                    </div>`, 
                    'success');
            } else {
                addResult('⚠️ Needs Improvement', 
                    `Score: ${finalScore}% - Some issues need to be resolved`, 
                    'warning');
            }
        }

        async function runCompleteTest() {
            clearResults();
            addResult('🚀 Complete Test Started', 'Running comprehensive API test suite...', 'info');
            
            await testGlobalObjects();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAllEndpoints();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await runFinalValidation();
        }

        // Auto-initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('🎯 API Test Tool Loaded', 'Click "Run Complete Test" to validate all functionality.', 'info');
                
                // Auto-run test after a short delay
                setTimeout(runCompleteTest, 1000);
            }, 500);
        });
    </script>
</body>
</html>
