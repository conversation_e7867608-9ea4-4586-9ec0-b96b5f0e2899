/* Admin Panel Modern Styles */
/* Extends modern-style.css with admin-specific styling */

/* Admin Layout */
.admin-container {
    display: flex;
    min-height: 100vh;
    background: var(--bg-light);
}

.admin-sidebar {
    width: 280px;
    background: var(--text-dark);
    color: white;
    padding: 2rem 1.5rem;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    border-right: 1px solid rgba(99, 102, 241, 0.2);
}

.admin-sidebar .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 2rem;
    display: block;
    text-decoration: none;
    text-align: center;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav-item {
    margin-bottom: 0.5rem;
}

.admin-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.admin-nav-link:hover,
.admin-nav-link.active {
    background: rgba(99, 102, 241, 0.2);
    color: white;
    transform: translateX(4px);
}

.admin-nav-link i {
    margin-right: 0.75rem;
    width: 20px;
    font-size: 1.1rem;
}

.admin-main {
    flex: 1;
    margin-left: 280px;
    min-height: 100vh;
}

.admin-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-header h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
}

.admin-header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-user-menu {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    background: var(--bg-light);
    border-radius: 8px;
    color: var(--text-dark);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.admin-user-menu:hover {
    background: var(--primary);
    color: white;
}

.admin-content {
    padding: 2rem;
}

/* Admin Cards */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.admin-stat-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--primary);
}

.admin-stat-card.success::before {
    background: var(--success);
}

.admin-stat-card.warning::before {
    background: var(--warning);
}

.admin-stat-card.danger::before {
    background: #ef4444;
}

.admin-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.admin-stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
}

.admin-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-light);
    color: var(--primary);
    font-size: 1.25rem;
}

.admin-stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin: 0;
    line-height: 1;
}

.admin-stat-change {
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.admin-stat-change.positive {
    color: var(--success);
}

.admin-stat-change.negative {
    color: #ef4444;
}

/* Admin Tables */
.admin-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.admin-table-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-table-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.admin-table-actions {
    display: flex;
    gap: 1rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem 2rem;
    text-align: left;
    border-bottom: 1px solid var(--border-light);
}

.admin-table th {
    font-weight: 600;
    color: var(--text-dark);
    background: var(--bg-light);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-table tbody tr:hover {
    background: rgba(99, 102, 241, 0.02);
}

.admin-table tbody tr:last-child td {
    border-bottom: none;
}

/* Admin Action Buttons */
.admin-actions {
    display: flex;
    gap: 0.5rem;
}

.admin-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-btn-primary {
    background: var(--primary);
    color: white;
}

.admin-btn-primary:hover {
    background: #4f46e5;
    transform: translateY(-1px);
}

.admin-btn-success {
    background: var(--success);
    color: white;
}

.admin-btn-success:hover {
    background: #059669;
    transform: translateY(-1px);
}

.admin-btn-warning {
    background: var(--warning);
    color: white;
}

.admin-btn-warning:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.admin-btn-danger {
    background: #ef4444;
    color: white;
}

.admin-btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.admin-btn-secondary {
    background: var(--bg-light);
    color: var(--text-dark);
    border: 1px solid var(--border-light);
}

.admin-btn-secondary:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

/* Admin Forms */
.admin-form-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-light);
}

.admin-form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.admin-form-group {
    margin-bottom: 1.5rem;
}

.admin-form-label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.admin-form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.admin-form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.admin-form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.admin-form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-light);
    border-radius: 8px;
    font-size: 1rem;
    resize: vertical;
    min-height: 100px;
}

/* Admin Alerts */
.admin-alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.admin-alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.admin-alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.admin-alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.admin-alert-info {
    background: rgba(99, 102, 241, 0.1);
    color: var(--primary);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

/* Responsive Admin */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .admin-sidebar.open {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-content {
        padding: 1rem;
    }
    
    .admin-header {
        padding: 1rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.75rem 1rem;
    }
    
    .admin-form-grid {
        grid-template-columns: 1fr;
    }
}
