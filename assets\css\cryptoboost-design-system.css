/* CryptoBoost Futuristic Design System */

:root {
    /* Futuristic Color Palette */
    --cyber-blue: #00d4ff;
    --cyber-purple: #8b5cf6;
    --cyber-pink: #ec4899;
    --cyber-green: #10b981;
    --cyber-orange: #f59e0b;
    --cyber-red: #ef4444;

    --neon-blue: #00f5ff;
    --neon-purple: #a855f7;
    --neon-pink: #f472b6;
    --neon-green: #34d399;
    --neon-orange: #fbbf24;

    --dark-bg: #0a0a0f;
    --dark-surface: #1a1a2e;
    --dark-card: #16213e;
    --dark-border: #0f3460;

    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);

    /* Primary Colors */
    --primary-50: #e6f7ff;
    --primary-100: #b3ecff;
    --primary-200: #80e1ff;
    --primary-300: #4dd6ff;
    --primary-400: #1acbff;
    --primary-500: var(--cyber-blue);
    --primary-600: #00b8e6;
    --primary-700: #009ccc;
    --primary-800: #0080b3;
    --primary-900: #006499;

    --secondary-50: #f3f0ff;
    --secondary-100: #e0d7ff;
    --secondary-200: #ccbeff;
    --secondary-300: #b9a5ff;
    --secondary-400: #a68cff;
    --secondary-500: var(--cyber-purple);
    --secondary-600: #7c3aed;
    --secondary-700: #6d28d9;
    --secondary-800: #5b21b6;
    --secondary-900: #4c1d95;

    /* Status Colors */
    --success: var(--cyber-green);
    --warning: var(--cyber-orange);
    --error: var(--cyber-red);
    --info: var(--cyber-blue);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    --text-inverse: #000000;

    /* Background Colors */
    --bg-primary: var(--dark-bg);
    --bg-secondary: var(--dark-surface);
    --bg-tertiary: var(--dark-card);
    --bg-glass: var(--glass-bg);

    /* Border Colors */
    --border-primary: var(--dark-border);
    --border-secondary: var(--glass-border);
    --border-accent: var(--cyber-blue);

    /* Typography */
    --font-family-sans: 'Orbitron', 'Inter', system-ui, -apple-system, sans-serif;
    --font-family-mono: 'Fira Code', 'JetBrains Mono', monospace;
    --font-family-display: 'Exo 2', 'Orbitron', sans-serif;

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-black: 900;

    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Border Radius */
    --radius-sm: 0.125rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-neon: 0 0 20px var(--cyber-blue);
    --shadow-glow: 0 0 30px rgba(0, 212, 255, 0.3);

    /* Transitions */
    --transition-fast: 150ms ease-out;
    --transition-normal: 300ms ease-out;
    --transition-slow: 500ms ease-out;

    /* Z-Index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* Futuristic Animations */
@keyframes neonPulse {
    0%, 100% {
        box-shadow: 0 0 5px var(--cyber-blue), 0 0 10px var(--cyber-blue), 0 0 15px var(--cyber-blue);
    }
    50% {
        box-shadow: 0 0 10px var(--cyber-blue), 0 0 20px var(--cyber-blue), 0 0 30px var(--cyber-blue);
    }
}

@keyframes dataFlow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes hologramFlicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes cyberpunkGlow {
    0%, 100% {
        text-shadow: 0 0 5px var(--cyber-blue), 0 0 10px var(--cyber-blue);
    }
    50% {
        text-shadow: 0 0 10px var(--cyber-blue), 0 0 20px var(--cyber-blue), 0 0 30px var(--cyber-blue);
    }
}

@keyframes matrixRain {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* Base Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-sans);
    background: var(--bg-primary);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

/* Futuristic Background Effects */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Cyberpunk Grid Background */
.cyber-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
    animation: hologramFlicker 3s ease-in-out infinite;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    margin: 0 0 var(--space-4) 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

h1 {
    font-size: var(--text-5xl);
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: cyberpunkGlow 2s ease-in-out infinite;
}

h2 {
    font-size: var(--text-4xl);
    color: var(--cyber-blue);
}

h3 {
    font-size: var(--text-3xl);
    color: var(--text-primary);
}

p {
    margin: 0 0 var(--space-4) 0;
    color: var(--text-secondary);
}

/* Futuristic Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-family: var(--font-family-sans);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    color: var(--text-primary);
    border-color: var(--cyber-blue);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    animation: neonPulse 1s ease-in-out infinite;
}

.btn-secondary {
    background: linear-gradient(135deg, var(--cyber-purple), var(--cyber-pink));
    color: var(--text-primary);
    border-color: var(--cyber-purple);
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
}

.btn-outline {
    background: transparent;
    color: var(--cyber-blue);
    border-color: var(--cyber-blue);
}

.btn-outline:hover {
    background: var(--cyber-blue);
    color: var(--text-inverse);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.4);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--glass-bg);
    color: var(--text-primary);
    border-color: var(--glass-border);
}

/* Button Sizes */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
}

.btn-xl {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-xl);
}

/* Futuristic Cards */
.card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
    border-color: var(--cyber-blue);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.2);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
}

.card-title {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.card-body {
    color: var(--text-secondary);
}

/* Futuristic Forms */
.form-group {
    margin-bottom: var(--space-6);
}

.form-label {
    display: block;
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-input {
    width: 100%;
    padding: var(--space-4);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-family: var(--font-family-mono);
    font-size: var(--text-base);
    transition: all var(--transition-normal);
    position: relative;
}

.form-input::placeholder {
    color: var(--text-muted);
}

.form-input:focus {
    outline: none;
    border-color: var(--cyber-blue);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    background: rgba(0, 212, 255, 0.05);
}