/* CryptoBoost Futuristic Admin Dashboard */

/* ===== ADMIN TERMINAL ===== */
.admin-terminal {
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.admin-terminal::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 10% 20%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(255, 69, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== COMMAND SIDEBAR ===== */
.command-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, var(--dark-surface), var(--dark-card));
    border-right: 2px solid var(--cyber-orange);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
    overflow-y: auto;
    overflow-x: hidden;
}

.command-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, var(--cyber-orange), var(--cyber-red));
    box-shadow: 0 0 20px var(--cyber-orange);
    animation: neonPulse 2s ease-in-out infinite;
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid rgba(255, 165, 0, 0.2);
    position: relative;
}

.command-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.brand-logo-admin {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    position: relative;
    animation: neonPulse 3s ease-in-out infinite;
}

.brand-logo-admin::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
    border-radius: var(--radius-xl);
    filter: blur(8px);
    opacity: 0.4;
    z-index: -1;
}

.brand-text-admin {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.brand-subtitle-admin {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-orange);
    text-transform: uppercase;
    letter-spacing: 0.2em;
}

.admin-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: rgba(255, 165, 0, 0.1);
    border: 1px solid var(--cyber-orange);
    border-radius: var(--radius-lg);
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-orange);
    text-transform: uppercase;
}

.admin-status-dot {
    width: 8px;
    height: 8px;
    background: var(--cyber-orange);
    border-radius: 50%;
    animation: neonPulse 1s ease-in-out infinite;
}

/* ===== COMMAND NAVIGATION ===== */
.command-nav {
    padding: var(--space-6) 0;
}

.nav-section {
    margin-bottom: var(--space-8);
}

.nav-section-title {
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: var(--cyber-orange);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0 var(--space-6);
    margin-bottom: var(--space-4);
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: var(--space-6);
    right: var(--space-6);
    height: 1px;
    background: linear-gradient(90deg, var(--cyber-orange), transparent);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--space-2);
}

.nav-link-admin {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--text-secondary);
    text-decoration: none;
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all var(--transition-normal);
    position: relative;
    border-left: 3px solid transparent;
}

.nav-link-admin::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(255, 165, 0, 0.1), transparent);
    transition: width var(--transition-normal);
}

.nav-link-admin:hover::before,
.nav-link-admin.active::before {
    width: 100%;
}

.nav-link-admin:hover,
.nav-link-admin.active {
    color: var(--cyber-orange);
    border-left-color: var(--cyber-orange);
    background: rgba(255, 165, 0, 0.05);
    text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

.nav-link-admin i {
    font-size: var(--text-lg);
    width: 20px;
    text-align: center;
}

/* ===== MAIN TERMINAL ===== */
.main-terminal {
    margin-left: 280px;
    min-height: 100vh;
    position: relative;
}

/* ===== COMMAND HEADER ===== */
.command-header {
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--cyber-orange);
    padding: var(--space-6);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.terminal-title {
    font-family: var(--font-family-display);
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.terminal-subtitle {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.system-stats {
    display: flex;
    gap: var(--space-6);
}

.stat-display-admin {
    text-align: center;
}

.stat-label-admin {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-1);
}

.stat-value-admin {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--cyber-orange);
    text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

.header-actions {
    display: flex;
    gap: var(--space-3);
}

.admin-notifications {
    position: relative;
}

.notification-btn-admin {
    background: var(--glass-bg);
    border: 1px solid var(--cyber-orange);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    color: var(--cyber-orange);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.notification-btn-admin:hover {
    background: rgba(255, 165, 0, 0.1);
    box-shadow: 0 0 20px rgba(255, 165, 0, 0.3);
}

.notification-badge-admin {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--cyber-red);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: neonPulse 1s ease-in-out infinite;
}

/* ===== TERMINAL CONTENT ===== */
.terminal-content {
    padding: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== COMMAND GRID ===== */
.command-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

/* ===== TERMINAL CARDS ===== */
.terminal-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.terminal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-orange), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.terminal-card:hover {
    transform: translateY(-5px);
    border-color: var(--cyber-orange);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(255, 165, 0, 0.2);
}

.terminal-card:hover::before {
    opacity: 1;
}

.card-header-admin {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
}

.card-title-admin {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-title-admin i {
    color: var(--cyber-orange);
    font-size: var(--text-lg);
}

.card-actions-admin {
    display: flex;
    gap: var(--space-2);
}

.card-body-admin {
    color: var(--text-secondary);
}

/* ===== METRICS GRID ===== */
.metrics-grid-admin {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
}

.metric-item-admin {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
}

.metric-item-admin:hover {
    background: rgba(255, 165, 0, 0.05);
    border-color: var(--cyber-orange);
    transform: translateY(-2px);
}

.metric-icon-admin {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    position: relative;
}

.metric-icon-admin.users {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
}

.metric-icon-admin.deposits {
    background: linear-gradient(135deg, var(--cyber-green), var(--cyber-blue));
}

.metric-icon-admin.withdrawals {
    background: linear-gradient(135deg, var(--cyber-red), var(--cyber-pink));
}

.metric-icon-admin.profit {
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
}

.metric-content-admin {
    flex: 1;
}

.metric-value-admin {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.metric-label-admin {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-1);
}

.metric-change-admin {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-semibold);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    text-transform: uppercase;
}

.metric-change-admin.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--cyber-green);
}

.metric-change-admin.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--cyber-red);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .command-sidebar {
        transform: translateX(-100%);
    }
    
    .command-sidebar.open {
        transform: translateX(0);
    }
    
    .main-terminal {
        margin-left: 0;
    }
    
    .command-grid {
        grid-template-columns: 1fr;
    }
    
    .system-stats {
        display: none;
    }
}

@media (max-width: 768px) {
    .terminal-content {
        padding: var(--space-4);
    }
    
    .header-container {
        flex-direction: column;
        gap: var(--space-4);
        align-items: flex-start;
    }
    
    .metrics-grid-admin {
        grid-template-columns: 1fr;
    }
    
    .metric-item-admin {
        flex-direction: column;
        text-align: center;
    }
}
