/* CryptoBoost Futuristic Authentication */

/* ===== AUTH BODY ===== */
.auth-body {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* ===== AUTH PARTICLES ===== */
.auth-particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* ===== AUTH CONTAINER ===== */
.auth-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    max-width: 1400px;
    width: 100%;
    min-height: 90vh;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-3xl);
    overflow: hidden;
    position: relative;
    z-index: 2;
    margin: var(--space-6);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), var(--cyber-purple), transparent);
    animation: dataFlow 3s ease-in-out infinite;
}

/* ===== AUTH BRANDING ===== */
.auth-branding {
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
    padding: var(--space-12);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.auth-branding::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 30% 70%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.branding-content {
    position: relative;
    z-index: 2;
}

.brand-logo-large {
    position: relative;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-5xl);
    color: white;
    margin: 0 auto var(--space-8);
    animation: neonPulse 3s ease-in-out infinite;
}

.logo-orbit {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
}

.orbit-ring {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid var(--cyber-blue);
    border-radius: 50%;
    opacity: 0.3;
    animation: rotate 10s linear infinite;
}

.orbit-ring:nth-child(2) {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-color: var(--cyber-purple);
    animation-duration: 15s;
    animation-direction: reverse;
}

.orbit-ring:nth-child(3) {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border-color: var(--cyber-pink);
    animation-duration: 20s;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.brand-title {
    font-family: var(--font-family-display);
    font-size: var(--text-5xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-subtitle {
    font-family: var(--font-family-mono);
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
    text-transform: uppercase;
    letter-spacing: 0.2em;
}

.brand-features {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: var(--text-secondary);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-item i {
    color: var(--cyber-blue);
    font-size: var(--text-lg);
}

.live-stats {
    display: flex;
    gap: var(--space-8);
}

.stat-display {
    text-align: center;
}

.stat-label {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-2);
}

.stat-value {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--cyber-blue);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.branding-visual {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.hologram-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
}

.hologram-layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid var(--cyber-blue);
    border-radius: 50%;
    opacity: 0.1;
    animation: hologramFlicker 4s ease-in-out infinite;
}

.hologram-layer:nth-child(2) {
    animation-delay: -1s;
    border-color: var(--cyber-purple);
}

.hologram-layer:nth-child(3) {
    animation-delay: -2s;
    border-color: var(--cyber-pink);
}

/* ===== AUTH PANEL ===== */
.auth-panel {
    padding: var(--space-12);
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: rgba(255, 255, 255, 0.02);
    position: relative;
}

.auth-header {
    text-align: center;
    margin-bottom: var(--space-10);
}

.auth-title {
    font-family: var(--font-family-display);
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-3);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.auth-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

.security-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--cyber-green);
    border-radius: var(--radius-full);
    color: var(--cyber-green);
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.security-dot {
    width: 8px;
    height: 8px;
    background: var(--cyber-green);
    border-radius: 50%;
    animation: neonPulse 1s ease-in-out infinite;
}

/* ===== CYBER INPUTS ===== */
.input-container {
    position: relative;
}

.cyber-input {
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    color: var(--text-primary);
    font-family: var(--font-family-mono);
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    position: relative;
    z-index: 2;
}

.cyber-input:focus {
    border-color: var(--cyber-blue);
    background: rgba(0, 212, 255, 0.05);
    box-shadow: 0 0 30px rgba(0, 212, 255, 0.2);
}

.input-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    opacity: 0;
    filter: blur(10px);
    transition: opacity var(--transition-normal);
    z-index: 1;
}

.cyber-input:focus + .input-glow {
    opacity: 0.3;
}

.password-toggle {
    position: absolute;
    right: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: var(--text-lg);
    z-index: 3;
    transition: color var(--transition-normal);
}

.password-toggle:hover {
    color: var(--cyber-blue);
}

/* ===== FORM OPTIONS ===== */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    cursor: pointer;
    position: relative;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: var(--glass-bg);
    border: 2px solid var(--glass-border);
    border-radius: var(--radius-md);
    position: relative;
    transition: all var(--transition-normal);
}

.checkmark::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 6px;
    width: 6px;
    height: 10px;
    border: solid var(--cyber-blue);
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: rgba(0, 212, 255, 0.1);
    border-color: var(--cyber-blue);
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    opacity: 1;
}

.checkbox-label {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.forgot-link {
    color: var(--cyber-blue);
    text-decoration: none;
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: all var(--transition-normal);
}

.forgot-link:hover {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* ===== AUTH SUBMIT BUTTON ===== */
.auth-submit {
    position: relative;
    overflow: hidden;
    margin-bottom: var(--space-8);
}

.btn-scan-line {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.auth-submit:hover .btn-scan-line {
    left: 100%;
}

/* ===== AUTH DIVIDER ===== */
.auth-divider {
    position: relative;
    text-align: center;
    margin: var(--space-8) 0;
    color: var(--text-muted);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.auth-divider span {
    background: var(--bg-primary);
    padding: 0 var(--space-4);
    position: relative;
    z-index: 1;
}

/* ===== DEMO ACCOUNTS ===== */
.demo-accounts {
    margin: var(--space-8) 0;
}

.demo-title {
    font-family: var(--font-family-display);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-align: center;
    margin-bottom: var(--space-6);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.demo-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

.demo-account {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-align: left;
}

.demo-account:hover {
    background: rgba(0, 212, 255, 0.05);
    border-color: var(--cyber-blue);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.demo-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    color: white;
}

.demo-icon.user {
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
}

.demo-icon.admin {
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
}

.demo-info {
    flex: 1;
}

.demo-label {
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-1);
}

.demo-email {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--text-muted);
}

/* ===== AUTH FOOTER ===== */
.auth-footer {
    text-align: center;
    margin-top: var(--space-8);
    padding-top: var(--space-6);
    border-top: 1px solid var(--glass-border);
}

.auth-footer p {
    color: var(--text-secondary);
    margin-bottom: var(--space-3);
}

.register-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--cyber-blue);
    text-decoration: none;
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all var(--transition-normal);
}

.register-link:hover {
    color: var(--cyber-purple);
    text-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

.back-to-home {
    text-align: center;
    margin-top: var(--space-6);
}

.home-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-muted);
    text-decoration: none;
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    transition: all var(--transition-normal);
}

.home-link:hover {
    color: var(--cyber-blue);
}

/* ===== BOOT SEQUENCE ===== */
.boot-sequence {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-slow);
}

.boot-sequence.active {
    opacity: 1;
    visibility: visible;
}

.boot-content {
    text-align: center;
    max-width: 500px;
}

.boot-logo {
    font-size: var(--text-6xl);
    color: var(--cyber-blue);
    margin-bottom: var(--space-8);
    animation: neonPulse 2s ease-in-out infinite;
}

.boot-text {
    margin-bottom: var(--space-8);
}

.boot-line {
    font-family: var(--font-family-mono);
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin-bottom: var(--space-3);
    opacity: 0;
    animation: bootTextAppear 0.5s ease-out forwards;
}

.boot-line:nth-child(1) { animation-delay: 0.5s; }
.boot-line:nth-child(2) { animation-delay: 1s; }
.boot-line:nth-child(3) { animation-delay: 1.5s; }
.boot-line:nth-child(4) { animation-delay: 2s; }

@keyframes bootTextAppear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.boot-progress {
    width: 100%;
    height: 4px;
    background: var(--glass-bg);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 3s ease-out;
    box-shadow: 0 0 10px var(--cyber-blue);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .auth-container {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: var(--space-4);
    }
    
    .auth-branding {
        padding: var(--space-8);
    }
    
    .auth-panel {
        padding: var(--space-8);
    }
    
    .demo-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .auth-container {
        margin: var(--space-2);
        min-height: 95vh;
    }
    
    .auth-branding,
    .auth-panel {
        padding: var(--space-6);
    }
    
    .brand-logo-large {
        width: 80px;
        height: 80px;
        font-size: var(--text-4xl);
    }
    
    .brand-title {
        font-size: var(--text-4xl);
    }
    
    .auth-title {
        font-size: var(--text-3xl);
    }
    
    .live-stats {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .form-options {
        flex-direction: column;
        gap: var(--space-4);
        align-items: flex-start;
    }
}
