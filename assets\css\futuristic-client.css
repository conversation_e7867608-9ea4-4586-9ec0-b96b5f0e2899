/* CryptoBoost Futuristic Client Dashboard */

/* ===== CLIENT TERMINAL ===== */
.client-terminal {
    background: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.client-terminal::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== TRADING SIDEBAR ===== */
.trading-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, var(--dark-surface), var(--dark-card));
    border-right: 2px solid var(--cyber-blue);
    z-index: var(--z-fixed);
    transition: all var(--transition-normal);
    overflow-y: auto;
    overflow-x: hidden;
}

.trading-sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, var(--cyber-blue), var(--cyber-purple));
    box-shadow: 0 0 20px var(--cyber-blue);
    animation: neonPulse 2s ease-in-out infinite;
}

.sidebar-header {
    padding: var(--space-6);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
}

.trading-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.brand-logo-client {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    position: relative;
    animation: neonPulse 3s ease-in-out infinite;
}

.logo-pulse {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    filter: blur(8px);
    opacity: 0.4;
    z-index: -1;
}

.brand-info {
    display: flex;
    flex-direction: column;
}

.brand-text-client {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.brand-subtitle-client {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.2em;
}

.client-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-3);
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid var(--cyber-blue);
    border-radius: var(--radius-lg);
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-blue);
    text-transform: uppercase;
}

.status-dot-client {
    width: 8px;
    height: 8px;
    background: var(--cyber-blue);
    border-radius: 50%;
    animation: neonPulse 1s ease-in-out infinite;
}

/* ===== TRADING NAVIGATION ===== */
.trading-nav {
    padding: var(--space-6) 0;
}

.nav-section {
    margin-bottom: var(--space-8);
}

.nav-section-title {
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    padding: 0 var(--space-6);
    margin-bottom: var(--space-4);
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: var(--space-6);
    right: var(--space-6);
    height: 1px;
    background: linear-gradient(90deg, var(--cyber-blue), transparent);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--space-2);
    position: relative;
}

.nav-link-client {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-6);
    color: var(--text-secondary);
    text-decoration: none;
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all var(--transition-normal);
    position: relative;
    border-left: 3px solid transparent;
    overflow: hidden;
}

.nav-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, rgba(0, 212, 255, 0.1), transparent);
    transition: width var(--transition-normal);
}

.nav-link-client:hover .nav-glow,
.nav-link-client.active .nav-glow {
    width: 100%;
}

.nav-link-client:hover,
.nav-link-client.active {
    color: var(--cyber-blue);
    border-left-color: var(--cyber-blue);
    background: rgba(0, 212, 255, 0.05);
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.nav-link-client i {
    font-size: var(--text-lg);
    width: 20px;
    text-align: center;
}

/* ===== SIDEBAR FOOTER ===== */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--space-6);
    border-top: 1px solid rgba(0, 212, 255, 0.2);
    background: linear-gradient(180deg, transparent, rgba(0, 0, 0, 0.3));
}

.trader-profile {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.trader-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
}

.avatar-status {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: var(--cyber-green);
    border: 2px solid var(--dark-surface);
    border-radius: 50%;
    animation: neonPulse 1s ease-in-out infinite;
}

.trader-info {
    flex: 1;
}

.trader-name {
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.trader-level {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.logout-btn-client {
    background: var(--glass-bg);
    border: 1px solid var(--cyber-red);
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    color: var(--cyber-red);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.logout-btn-client:hover {
    background: rgba(239, 68, 68, 0.1);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

/* ===== MAIN TRADING TERMINAL ===== */
.main-trading-terminal {
    margin-left: 280px;
    min-height: 100vh;
    position: relative;
}

/* ===== TRADING HEADER ===== */
.trading-header {
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 2px solid var(--cyber-blue);
    padding: var(--space-6);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.mobile-sidebar-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.mobile-sidebar-toggle span {
    width: 25px;
    height: 2px;
    background: var(--cyber-blue);
    transition: all var(--transition-normal);
}

.terminal-info {
    display: flex;
    flex-direction: column;
}

.terminal-title {
    font-family: var(--font-family-display);
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.terminal-subtitle {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.portfolio-summary {
    text-align: center;
}

.balance-display {
    padding: var(--space-4) var(--space-6);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    position: relative;
}

.balance-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    animation: dataFlow 3s ease-in-out infinite;
}

.balance-label {
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: var(--space-2);
}

.balance-amount {
    font-family: var(--font-family-display);
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-black);
    color: var(--cyber-blue);
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
    margin-bottom: var(--space-2);
}

.balance-change {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
}

.balance-change.positive {
    color: var(--cyber-green);
}

.balance-change.negative {
    color: var(--cyber-red);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-6);
}

.trading-actions {
    display: flex;
    gap: var(--space-3);
}

.notifications-client {
    position: relative;
}

.notification-btn-client {
    background: var(--glass-bg);
    border: 1px solid var(--cyber-blue);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    color: var(--cyber-blue);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.notification-btn-client:hover {
    background: rgba(0, 212, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.notification-badge-client {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--cyber-red);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: neonPulse 1s ease-in-out infinite;
}

/* ===== TRADING CONTENT ===== */
.trading-content {
    padding: var(--space-8);
    max-width: 1400px;
    margin: 0 auto;
}

/* ===== TRADING GRID ===== */
.trading-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

/* ===== TRADING CARDS ===== */
.trading-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.trading-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.trading-card:hover {
    transform: translateY(-5px);
    border-color: var(--cyber-blue);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.2);
}

.trading-card:hover::before {
    opacity: 1;
}

.card-header-client {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
}

.card-title-client {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-title-client i {
    color: var(--cyber-blue);
    font-size: var(--text-lg);
}

.card-actions-client {
    display: flex;
    gap: var(--space-2);
}

.card-body-client {
    color: var(--text-secondary);
}

/* ===== CRYPTO ASSETS ===== */
.crypto-assets {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.asset-item-client {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
}

.asset-item-client:hover {
    background: rgba(0, 212, 255, 0.05);
    border-color: var(--cyber-blue);
    transform: translateY(-2px);
}

.asset-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-family-display);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-bold);
    color: white;
    text-transform: uppercase;
}

.asset-icon.btc {
    background: linear-gradient(135deg, #f7931a, #ff9500);
}

.asset-icon.eth {
    background: linear-gradient(135deg, #627eea, #8a92b2);
}

.asset-icon.ada {
    background: linear-gradient(135deg, #0033ad, #1e3a8a);
}

.asset-info {
    flex: 1;
}

.asset-name {
    font-family: var(--font-family-display);
    font-size: var(--text-base);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.asset-amount {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.asset-value {
    font-family: var(--font-family-display);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-right: var(--space-3);
}

.asset-change {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-semibold);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
}

.asset-change.positive {
    background: rgba(16, 185, 129, 0.1);
    color: var(--cyber-green);
}

.asset-change.negative {
    background: rgba(239, 68, 68, 0.1);
    color: var(--cyber-red);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .trading-sidebar {
        transform: translateX(-100%);
    }
    
    .trading-sidebar.open {
        transform: translateX(0);
    }
    
    .main-trading-terminal {
        margin-left: 0;
    }
    
    .mobile-sidebar-toggle {
        display: flex;
    }
    
    .trading-grid {
        grid-template-columns: 1fr;
    }
    
    .header-center {
        display: none;
    }
}

@media (max-width: 768px) {
    .trading-content {
        padding: var(--space-4);
    }
    
    .header-container {
        flex-direction: column;
        gap: var(--space-4);
        align-items: flex-start;
    }
    
    .trading-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .asset-item-client {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }
    
    .asset-info {
        text-align: center;
    }
}
