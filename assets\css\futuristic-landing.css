/* CryptoBoost Futuristic Landing Page */

/* ===== MATRIX RAIN EFFECT ===== */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.matrix-char {
    position: absolute;
    color: var(--cyber-green);
    font-family: var(--font-family-mono);
    font-size: 14px;
    animation: matrixRain 3s linear infinite;
    opacity: 0.7;
}

/* ===== CYBER NAVIGATION ===== */
.cyber-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.cyber-nav.scrolled {
    background: rgba(10, 10, 15, 0.95);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    position: relative;
}

.brand-logo {
    position: relative;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    animation: neonPulse 2s ease-in-out infinite;
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    filter: blur(10px);
    opacity: 0.3;
    z-index: -1;
}

.brand-text {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.brand-subtitle {
    position: absolute;
    bottom: -8px;
    left: 60px;
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.2em;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--cyber-blue);
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--cyber-blue);
    transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--cyber-blue);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-8);
    position: relative;
    animation: hologramFlicker 3s ease-in-out infinite;
}

.badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-full);
    filter: blur(8px);
    opacity: 0.3;
    z-index: -1;
}

.hero-title {
    font-family: var(--font-family-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: var(--font-weight-black);
    line-height: 1.1;
    margin-bottom: var(--space-6);
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.title-line {
    display: block;
    color: var(--text-primary);
}

.title-highlight {
    display: block;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple), var(--cyber-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: cyberpunkGlow 3s ease-in-out infinite;
    position: relative;
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-8);
    max-width: 600px;
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
    margin-bottom: var(--space-10);
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-family: var(--font-family-display);
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-black);
    color: var(--cyber-blue);
    display: block;
    margin-bottom: var(--space-2);
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.stat-label {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.hero-actions {
    display: flex;
    gap: var(--space-6);
    flex-wrap: wrap;
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    border-radius: inherit;
}

/* ===== TRADING INTERFACE ===== */
.hero-visual {
    position: relative;
    z-index: 2;
}

.trading-interface {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    animation: hologramFlicker 4s ease-in-out infinite;
}

.trading-interface::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    animation: dataFlow 3s ease-in-out infinite;
}

.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
}

.interface-title {
    font-family: var(--font-family-display);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.interface-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--cyber-green);
    text-transform: uppercase;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--cyber-green);
    animation: neonPulse 1s ease-in-out infinite;
}

.crypto-ticker {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.ticker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-lg);
    font-family: var(--font-family-mono);
}

.crypto-symbol {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.crypto-price {
    color: var(--text-secondary);
}

.crypto-change {
    font-weight: var(--font-weight-semibold);
}

.crypto-change.positive {
    color: var(--cyber-green);
}

.crypto-change.negative {
    color: var(--cyber-red);
}

.chart-container {
    height: 200px;
    margin-bottom: var(--space-6);
    position: relative;
}

.trading-controls {
    display: flex;
    gap: var(--space-3);
}

.control-btn {
    flex: 1;
    padding: var(--space-3);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.control-btn.active,
.control-btn:hover {
    background: var(--cyber-blue);
    color: var(--text-primary);
    border-color: var(--cyber-blue);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-crypto {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--cyber-blue);
    animation: float 6s ease-in-out infinite;
}

.floating-crypto.btc {
    top: 20%;
    right: 10%;
    color: var(--cyber-orange);
    animation-delay: -2s;
}

.floating-crypto.eth {
    bottom: 30%;
    right: 20%;
    color: var(--cyber-purple);
    animation-delay: -4s;
}

.floating-crypto:nth-child(3) {
    top: 60%;
    right: 5%;
    animation-delay: -1s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* ===== PARTICLES EFFECT ===== */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--cyber-blue);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--space-12);
        text-align: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--space-4);
    }
    
    .hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-container {
        padding: 0 var(--space-4);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-xl {
        width: 100%;
        max-width: 300px;
    }
}
