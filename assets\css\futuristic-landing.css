/* CryptoBoost Futuristic Landing Page */

/* ===== MATRIX RAIN EFFECT ===== */
.matrix-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.matrix-char {
    position: absolute;
    color: var(--cyber-green);
    font-family: var(--font-family-mono);
    font-size: 14px;
    animation: matrixRain 3s linear infinite;
    opacity: 0.7;
}

/* ===== CYBER NAVIGATION ===== */
.cyber-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.cyber-nav.scrolled {
    background: rgba(10, 10, 15, 0.95);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    position: relative;
}

.brand-logo {
    position: relative;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-xl);
    color: white;
    animation: neonPulse 2s ease-in-out infinite;
}

.logo-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    filter: blur(10px);
    opacity: 0.3;
    z-index: -1;
}

.brand-text {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.brand-subtitle {
    position: absolute;
    bottom: -8px;
    left: 60px;
    font-family: var(--font-family-mono);
    font-size: var(--text-xs);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.2em;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-8);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    color: var(--text-secondary);
    text-decoration: none;
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left var(--transition-normal);
}

.nav-link:hover::before,
.nav-link.active::before {
    left: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--cyber-blue);
    background: rgba(0, 212, 255, 0.1);
    transform: translateY(-2px);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--space-2);
}

.nav-toggle span {
    width: 25px;
    height: 2px;
    background: var(--cyber-blue);
    transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

.hero-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
}

.hero-content {
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-full);
    color: var(--cyber-blue);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-8);
    position: relative;
    animation: hologramFlicker 3s ease-in-out infinite;
}

.badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-full);
    filter: blur(8px);
    opacity: 0.3;
    z-index: -1;
}

.hero-title {
    font-family: var(--font-family-display);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: var(--font-weight-black);
    line-height: 1.1;
    margin-bottom: var(--space-6);
    text-transform: uppercase;
    letter-spacing: 0.02em;
}

.title-line {
    display: block;
    color: var(--text-primary);
}

.title-highlight {
    display: block;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple), var(--cyber-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: cyberpunkGlow 3s ease-in-out infinite;
    position: relative;
}

.hero-description {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-8);
    max-width: 600px;
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
    margin-bottom: var(--space-10);
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-family: var(--font-family-display);
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-black);
    color: var(--cyber-blue);
    display: block;
    margin-bottom: var(--space-2);
    text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.stat-label {
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.hero-actions {
    display: flex;
    gap: var(--space-6);
    flex-wrap: wrap;
}

.btn-particles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    border-radius: inherit;
}

/* ===== TRADING INTERFACE ===== */
.hero-visual {
    position: relative;
    z-index: 2;
}

.trading-interface {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    animation: hologramFlicker 4s ease-in-out infinite;
}

.trading-interface::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    animation: dataFlow 3s ease-in-out infinite;
}

.interface-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid var(--glass-border);
}

.interface-title {
    font-family: var(--font-family-display);
    font-size: var(--text-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.interface-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-family: var(--font-family-mono);
    font-size: var(--text-sm);
    color: var(--cyber-green);
    text-transform: uppercase;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--cyber-green);
    animation: neonPulse 1s ease-in-out infinite;
}

.crypto-ticker {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.ticker-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.02);
    border-radius: var(--radius-lg);
    font-family: var(--font-family-mono);
}

.crypto-symbol {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.crypto-price {
    color: var(--text-secondary);
}

.crypto-change {
    font-weight: var(--font-weight-semibold);
}

.crypto-change.positive {
    color: var(--cyber-green);
}

.crypto-change.negative {
    color: var(--cyber-red);
}

.chart-container {
    height: 200px;
    margin-bottom: var(--space-6);
    position: relative;
}

.trading-controls {
    display: flex;
    gap: var(--space-3);
}

.control-btn {
    flex: 1;
    padding: var(--space-3);
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.control-btn.active,
.control-btn:hover {
    background: var(--cyber-blue);
    color: var(--text-primary);
    border-color: var(--cyber-blue);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

/* ===== FLOATING ELEMENTS ===== */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.floating-crypto {
    position: absolute;
    width: 60px;
    height: 60px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: var(--cyber-blue);
    animation: float 6s ease-in-out infinite;
}

.floating-crypto.btc {
    top: 20%;
    right: 10%;
    color: var(--cyber-orange);
    animation-delay: -2s;
}

.floating-crypto.eth {
    bottom: 30%;
    right: 20%;
    color: var(--cyber-purple);
    animation-delay: -4s;
}

.floating-crypto:nth-child(3) {
    top: 60%;
    right: 5%;
    animation-delay: -1s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* ===== PARTICLES EFFECT ===== */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--cyber-blue);
    border-radius: 50%;
    animation: particleFloat 8s linear infinite;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}

/* ===== FEATURES SECTION ===== */
.features-section {
    padding: var(--space-20) 0;
    background: linear-gradient(180deg, var(--bg-primary), var(--dark-surface));
    position: relative;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.feature-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--cyber-blue);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.2);
}

.feature-card:hover::before {
    opacity: 1;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    color: white;
    position: relative;
}

.icon-glow {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-2xl);
    filter: blur(10px);
    opacity: 0.4;
    z-index: -1;
}

.feature-title {
    font-family: var(--font-family-display);
    font-size: var(--text-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.feature-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
    line-height: 1.6;
}

.feature-stats {
    padding: var(--space-3) var(--space-4);
    background: rgba(0, 212, 255, 0.1);
    border: 1px solid var(--cyber-blue);
    border-radius: var(--radius-lg);
    display: inline-block;
}

.feature-stats .stat {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    color: var(--cyber-blue);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== TRADING DEMO SECTION ===== */
.trading-demo-section {
    padding: var(--space-20) 0;
    background: var(--dark-surface);
    position: relative;
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
    align-items: center;
}

.demo-features {
    margin: var(--space-8) 0;
}

.demo-feature {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    color: var(--text-secondary);
}

.demo-feature i {
    color: var(--cyber-green);
    font-size: var(--text-lg);
}

.demo-actions {
    display: flex;
    gap: var(--space-4);
    margin-top: var(--space-8);
}

.demo-visual {
    position: relative;
}

.demo-interface {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.interface-header {
    background: linear-gradient(90deg, var(--dark-card), var(--dark-surface));
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--glass-border);
}

.interface-title {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.interface-controls {
    display: flex;
    gap: var(--space-2);
}

.control-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control-dot.red { background: #ef4444; }
.control-dot.yellow { background: #f59e0b; }
.control-dot.green { background: #10b981; }

.interface-body {
    padding: var(--space-6);
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-6);
}

.trading-chart {
    height: 200px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--radius-xl);
    position: relative;
}

.trading-panel {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-4);
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--glass-border);
}

.panel-title {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.panel-status {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
}

.panel-status.online {
    background: rgba(16, 185, 129, 0.2);
    color: var(--cyber-green);
}

.order-tabs {
    display: flex;
    margin-bottom: var(--space-4);
}

.order-tab {
    flex: 1;
    padding: var(--space-2) var(--space-3);
    background: transparent;
    border: 1px solid var(--glass-border);
    color: var(--text-secondary);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.order-tab:first-child {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.order-tab:last-child {
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    border-left: none;
}

.order-tab.active {
    background: var(--cyber-green);
    color: white;
    border-color: var(--cyber-green);
}

.order-inputs {
    margin-bottom: var(--space-4);
}

.input-group {
    margin-bottom: var(--space-3);
}

.input-group label {
    display: block;
    font-size: var(--text-xs);
    color: var(--text-muted);
    margin-bottom: var(--space-1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: var(--space-2) var(--space-3);
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    font-size: var(--text-sm);
}

.order-submit {
    width: 100%;
    padding: var(--space-3);
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border: none;
    border-radius: var(--radius-lg);
    color: white;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.order-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--space-12);
        text-align: center;
    }
    
    .hero-stats {
        justify-content: center;
    }
    
    .nav-menu {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 var(--space-4);
    }
    
    .hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-container {
        padding: 0 var(--space-4);
    }
    
    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-xl {
        width: 100%;
        max-width: 300px;
    }

    .demo-content {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .demo-actions {
        flex-direction: column;
    }

    .interface-body {
        grid-template-columns: 1fr;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .plans-grid {
        grid-template-columns: 1fr;
    }

    .plan-card.featured {
        transform: none;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .trust-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .trust-badges {
        flex-direction: column;
        align-items: center;
    }

    .guarantee-content {
        flex-direction: column;
        text-align: center;
    }

    .cta-content {
        text-align: center;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* ===== PLANS SECTION ===== */
.plans-section {
    padding: var(--space-20) 0;
    background: linear-gradient(180deg, var(--dark-surface), var(--bg-primary));
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.plan-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.plan-card.featured {
    border-color: var(--cyber-blue);
    transform: scale(1.05);
    box-shadow: 0 25px 50px rgba(0, 212, 255, 0.2);
}

.plan-card:hover {
    transform: translateY(-10px);
    border-color: var(--cyber-blue);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.2);
}

.plan-card.featured:hover {
    transform: scale(1.05) translateY(-10px);
}

.plan-badge {
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-red));
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.plan-header {
    margin-bottom: var(--space-8);
}

.plan-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-4);
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-3xl);
    color: white;
}

.plan-name {
    font-family: var(--font-family-display);
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-black);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.plan-price {
    margin-bottom: var(--space-6);
}

.price-amount {
    font-family: var(--font-family-display);
    font-size: var(--text-4xl);
    font-weight: var(--font-weight-black);
    color: var(--cyber-blue);
    display: block;
}

.price-period {
    font-size: var(--text-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.plan-features {
    margin-bottom: var(--space-8);
    text-align: left;
}

.plan-feature {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    color: var(--text-secondary);
}

.plan-feature i {
    color: var(--cyber-green);
    font-size: var(--text-lg);
}

.plan-btn {
    width: 100%;
    padding: var(--space-4);
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border: none;
    border-radius: var(--radius-xl);
    color: white;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
}

.plan-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.plans-guarantee {
    margin-top: var(--space-12);
    text-align: center;
}

.guarantee-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    padding: var(--space-6);
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid var(--cyber-green);
    border-radius: var(--radius-2xl);
    max-width: 600px;
    margin: 0 auto;
}

.guarantee-icon {
    font-size: var(--text-3xl);
    color: var(--cyber-green);
}

.guarantee-text h4 {
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
}

.guarantee-text p {
    color: var(--text-secondary);
    margin: 0;
}

/* ===== TRUST SECTION ===== */
.trust-section {
    padding: var(--space-16) 0;
    background: var(--dark-card);
}

.trust-content {
    text-align: center;
}

.trust-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-12);
}

.trust-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    color: white;
}

.stat-number {
    font-family: var(--font-family-display);
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-black);
    color: var(--cyber-blue);
}

.stat-label {
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: var(--text-sm);
}

.trust-badges {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.trust-badge i {
    color: var(--cyber-green);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials-section {
    padding: var(--space-20) 0;
    background: var(--bg-primary);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-12);
}

.testimonial-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    transition: all var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    border-color: var(--cyber-blue);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.1);
}

.testimonial-content {
    margin-bottom: var(--space-6);
}

.testimonial-stars {
    display: flex;
    gap: var(--space-1);
    margin-bottom: var(--space-4);
}

.testimonial-stars i {
    color: var(--cyber-orange);
    font-size: var(--text-lg);
}

.testimonial-text {
    color: var(--text-secondary);
    font-style: italic;
    line-height: 1.6;
    margin: 0;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--text-lg);
}

.author-name {
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.author-title {
    font-size: var(--text-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== FINAL CTA SECTION ===== */
.final-cta-section {
    padding: var(--space-20) 0;
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-card));
    position: relative;
    overflow: hidden;
}

.final-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.cta-content {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: var(--space-12);
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
}

.cta-visual {
    display: flex;
    justify-content: center;
}

.cta-icon {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-5xl);
    color: white;
    position: relative;
    animation: neonPulse 3s ease-in-out infinite;
}

.icon-orbit {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
}

.orbit-ring {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 1px solid var(--cyber-blue);
    border-radius: 50%;
    opacity: 0.3;
    animation: rotate 10s linear infinite;
}

.orbit-ring:nth-child(2) {
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border-color: var(--cyber-purple);
    animation-duration: 15s;
    animation-direction: reverse;
}

.orbit-ring:nth-child(3) {
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border-color: var(--cyber-pink);
    animation-duration: 20s;
}

.cta-title {
    font-family: var(--font-family-display);
    font-size: var(--text-5xl);
    font-weight: var(--font-weight-black);
    margin-bottom: var(--space-6);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.cta-description {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
    max-width: 600px;
}

.cta-features {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
}

.cta-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-3);
    color: var(--text-secondary);
}

.cta-feature i {
    color: var(--cyber-green);
    font-size: var(--text-lg);
}

.cta-actions {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-6);
}

.cta-btn {
    position: relative;
    overflow: hidden;
}

.cta-guarantee {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-muted);
    font-size: var(--text-sm);
}

.cta-guarantee i {
    color: var(--cyber-green);
}

/* ===== FOOTER ===== */
.cyber-footer {
    background: var(--dark-card);
    border-top: 1px solid var(--glass-border);
    padding: var(--space-16) 0 var(--space-8);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--space-12);
    margin-bottom: var(--space-12);
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.footer-brand .brand-logo {
    width: 60px;
    height: 60px;
}

.footer-brand .brand-text {
    font-size: var(--text-2xl);
}

.brand-description {
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 400px;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
}

.footer-section h4 {
    font-family: var(--font-family-display);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.footer-section a {
    display: block;
    color: var(--text-secondary);
    text-decoration: none;
    margin-bottom: var(--space-3);
    transition: color var(--transition-normal);
}

.footer-section a:hover {
    color: var(--cyber-blue);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-8);
    border-top: 1px solid var(--glass-border);
}

.footer-copyright {
    color: var(--text-muted);
    font-size: var(--text-sm);
}

.footer-social {
    display: flex;
    gap: var(--space-4);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.social-link:hover {
    background: var(--cyber-blue);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}
