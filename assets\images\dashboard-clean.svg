<svg width="800" height="500" viewBox="0 0 800 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="cleanGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cleanGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cleanGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <filter id="cleanShadow">
      <feDropShadow dx="0" dy="8" stdDeviation="16" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="#f8fafc" rx="20"/>
  
  <!-- Browser mockup -->
  <g transform="translate(50, 50)">
    <!-- Browser window -->
    <rect x="0" y="0" width="700" height="400" rx="12" fill="white" stroke="#e2e8f0" stroke-width="1" filter="url(#cleanShadow)"/>
    
    <!-- Browser header -->
    <rect x="0" y="0" width="700" height="40" rx="12" fill="#f8fafc"/>
    <rect x="0" y="28" width="700" height="12" fill="#f8fafc"/>
    
    <!-- Browser controls -->
    <circle cx="20" cy="20" r="5" fill="#ef4444"/>
    <circle cx="40" cy="20" r="5" fill="#f59e0b"/>
    <circle cx="60" cy="20" r="5" fill="#10b981"/>
    
    <!-- URL bar -->
    <rect x="100" y="12" width="300" height="16" rx="8" fill="white" stroke="#e2e8f0"/>
    <text x="110" y="22" fill="#64748b" font-size="10">cryptoboost.fr/dashboard</text>
    
    <!-- Main content -->
    <g transform="translate(0, 50)">
      <!-- Sidebar -->
      <rect x="0" y="0" width="200" height="350" fill="#0f172a"/>
      
      <!-- Logo -->
      <rect x="20" y="20" width="160" height="30" rx="6" fill="url(#cleanGrad1)"/>
      <text x="100" y="38" text-anchor="middle" fill="white" font-size="14" font-weight="bold">CryptoBoost</text>
      
      <!-- Navigation -->
      <rect x="20" y="70" width="160" height="30" rx="6" fill="rgba(99, 102, 241, 0.2)"/>
      <text x="30" y="88" fill="white" font-size="12">📊 Dashboard</text>
      
      <rect x="20" y="110" width="160" height="30" rx="6" fill="rgba(255,255,255,0.05)"/>
      <text x="30" y="128" fill="rgba(255,255,255,0.7)" font-size="12">💰 Portefeuille</text>
      
      <rect x="20" y="150" width="160" height="30" rx="6" fill="rgba(255,255,255,0.05)"/>
      <text x="30" y="168" fill="rgba(255,255,255,0.7)" font-size="12">🤖 Trading Bots</text>
      
      <rect x="20" y="190" width="160" height="30" rx="6" fill="rgba(255,255,255,0.05)"/>
      <text x="30" y="208" fill="rgba(255,255,255,0.7)" font-size="12">📈 Historique</text>
      
      <!-- Main dashboard area -->
      <g transform="translate(220, 0)">
        <!-- Header -->
        <rect x="0" y="0" width="480" height="60" fill="white"/>
        <text x="20" y="25" fill="#0f172a" font-size="18" font-weight="bold">Tableau de Bord</text>
        <text x="20" y="45" fill="#64748b" font-size="12">Bienvenue, utilisateur</text>
        
        <!-- Live status -->
        <circle cx="420" cy="30" r="4" fill="#10b981">
          <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
        </circle>
        <text x="435" y="34" fill="#10b981" font-size="12" font-weight="bold">LIVE</text>
        
        <!-- Stats grid -->
        <g transform="translate(0, 80)">
          <!-- Portfolio card -->
          <rect x="0" y="0" width="110" height="80" rx="8" fill="white" stroke="#e2e8f0"/>
          <rect x="8" y="8" width="94" height="4" rx="2" fill="url(#cleanGrad1)"/>
          <text x="55" y="30" text-anchor="middle" fill="#64748b" font-size="10">Portfolio Total</text>
          <text x="55" y="50" text-anchor="middle" fill="#0f172a" font-size="16" font-weight="bold">€24,567</text>
          <text x="55" y="68" text-anchor="middle" fill="#10b981" font-size="12">+12.8% ↗</text>
          
          <!-- Daily profit -->
          <rect x="125" y="0" width="110" height="80" rx="8" fill="white" stroke="#e2e8f0"/>
          <rect x="133" y="8" width="94" height="4" rx="2" fill="url(#cleanGrad2)"/>
          <text x="180" y="30" text-anchor="middle" fill="#64748b" font-size="10">Profit 24h</text>
          <text x="180" y="50" text-anchor="middle" fill="#0f172a" font-size="16" font-weight="bold">€1,247</text>
          <text x="180" y="68" text-anchor="middle" fill="#10b981" font-size="12">+8.4% ↗</text>
          
          <!-- Active bots -->
          <rect x="250" y="0" width="110" height="80" rx="8" fill="white" stroke="#e2e8f0"/>
          <rect x="258" y="8" width="94" height="4" rx="2" fill="url(#cleanGrad3)"/>
          <text x="305" y="30" text-anchor="middle" fill="#64748b" font-size="10">Bots Actifs</text>
          <text x="305" y="50" text-anchor="middle" fill="#0f172a" font-size="16" font-weight="bold">3</text>
          <text x="305" y="68" text-anchor="middle" fill="#10b981" font-size="12">En cours</text>
          
          <!-- Win rate -->
          <rect x="375" y="0" width="105" height="80" rx="8" fill="white" stroke="#e2e8f0"/>
          <rect x="383" y="8" width="89" height="4" rx="2" fill="url(#cleanGrad1)"/>
          <text x="427" y="30" text-anchor="middle" fill="#64748b" font-size="10">Taux Réussite</text>
          <text x="427" y="50" text-anchor="middle" fill="#0f172a" font-size="16" font-weight="bold">87%</text>
          <text x="427" y="68" text-anchor="middle" fill="#10b981" font-size="12">Excellent</text>
        </g>
        
        <!-- Chart section -->
        <g transform="translate(0, 180)">
          <rect x="0" y="0" width="480" height="160" rx="8" fill="white" stroke="#e2e8f0"/>
          <text x="20" y="25" fill="#0f172a" font-size="14" font-weight="bold">Performance en Temps Réel</text>
          <text x="20" y="45" fill="#64748b" font-size="12">BTC/USDT • ETH/USDT • ADA/USDT</text>
          
          <!-- Chart grid -->
          <g stroke="#f1f5f9" stroke-width="1">
            <line x1="40" y1="60" x2="460" y2="60"/>
            <line x1="40" y1="80" x2="460" y2="80"/>
            <line x1="40" y1="100" x2="460" y2="100"/>
            <line x1="40" y1="120" x2="460" y2="120"/>
            <line x1="40" y1="140" x2="460" y2="140"/>
          </g>
          
          <!-- Performance line -->
          <polyline points="40,130 80,125 120,115 160,110 200,100 240,95 280,90 320,95 360,85 400,80 440,85 460,75" 
                    stroke="url(#cleanGrad2)" stroke-width="3" fill="none"/>
          
          <!-- Area under curve -->
          <polygon points="40,140 80,125 120,115 160,110 200,100 240,95 280,90 320,95 360,85 400,80 440,85 460,75 460,140" 
                   fill="url(#cleanGrad2)" opacity="0.1"/>
          
          <!-- Data points -->
          <circle cx="200" cy="100" r="3" fill="#10b981">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
          </circle>
          <circle cx="320" cy="95" r="3" fill="#10b981">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="0.5s"/>
          </circle>
          <circle cx="440" cy="85" r="3" fill="#10b981">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="1s"/>
          </circle>
        </g>
      </g>
    </g>
  </g>
  
  <!-- Floating elements -->
  <circle cx="100" cy="100" r="2" fill="url(#cleanGrad1)" opacity="0.6">
    <animate attributeName="cy" values="100;90;100" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="700" cy="150" r="2" fill="url(#cleanGrad2)" opacity="0.6">
    <animate attributeName="cy" values="150;140;150" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="750" cy="400" r="2" fill="url(#cleanGrad3)" opacity="0.6">
    <animate attributeName="cy" values="400;390;400" dur="6s" repeatCount="indefinite"/>
  </circle>
</svg>
