<svg width="800" height="500" viewBox="0 0 800 500" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dashMockGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dashMockGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dashMockGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="dashMockGrad4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <filter id="dashMockGlow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <filter id="shadow">
      <feDropShadow dx="0" dy="10" stdDeviation="20" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="500" fill="rgba(10, 14, 39, 0.95)" rx="20"/>
  
  <!-- Browser window mockup -->
  <g transform="translate(50, 30)">
    <!-- Browser frame -->
    <rect x="0" y="0" width="700" height="440" rx="15" fill="rgba(26, 31, 58, 0.9)" stroke="rgba(102, 126, 234, 0.3)" stroke-width="2" filter="url(#shadow)"/>
    
    <!-- Browser header -->
    <rect x="0" y="0" width="700" height="40" rx="15" fill="rgba(102, 126, 234, 0.2)"/>
    <rect x="0" y="25" width="700" height="15" fill="rgba(102, 126, 234, 0.2)"/>
    
    <!-- Browser controls -->
    <circle cx="20" cy="20" r="6" fill="#ff5f57"/>
    <circle cx="40" cy="20" r="6" fill="#ffbd2e"/>
    <circle cx="60" cy="20" r="6" fill="#28ca42"/>
    
    <!-- URL bar -->
    <rect x="100" y="12" width="400" height="16" rx="8" fill="rgba(255,255,255,0.1)"/>
    <text x="110" y="22" fill="rgba(255,255,255,0.6)" font-size="10">https://cryptoboost.fr/dashboard</text>
    
    <!-- Main dashboard content -->
    <g transform="translate(0, 50)">
      <!-- Sidebar -->
      <rect x="0" y="0" width="150" height="390" fill="rgba(10, 14, 39, 0.8)"/>
      
      <!-- Logo area -->
      <rect x="20" y="20" width="110" height="30" rx="5" fill="url(#dashMockGrad1)" opacity="0.8"/>
      <text x="75" y="38" text-anchor="middle" fill="white" font-size="12" font-weight="bold">CryptoBoost</text>
      
      <!-- Navigation items -->
      <rect x="20" y="70" width="110" height="25" rx="5" fill="url(#dashMockGrad1)" opacity="0.6"/>
      <text x="30" y="85" fill="white" font-size="10">📊 Dashboard</text>
      
      <rect x="20" y="105" width="110" height="25" rx="5" fill="rgba(255,255,255,0.1)"/>
      <text x="30" y="120" fill="rgba(255,255,255,0.7)" font-size="10">💰 Portefeuille</text>
      
      <rect x="20" y="140" width="110" height="25" rx="5" fill="rgba(255,255,255,0.1)"/>
      <text x="30" y="155" fill="rgba(255,255,255,0.7)" font-size="10">🤖 Bots IA</text>
      
      <rect x="20" y="175" width="110" height="25" rx="5" fill="rgba(255,255,255,0.1)"/>
      <text x="30" y="190" fill="rgba(255,255,255,0.7)" font-size="10">📈 Historique</text>
      
      <rect x="20" y="210" width="110" height="25" rx="5" fill="rgba(255,255,255,0.1)"/>
      <text x="30" y="225" fill="rgba(255,255,255,0.7)" font-size="10">⚙️ Paramètres</text>
      
      <!-- Main content area -->
      <g transform="translate(170, 0)">
        <!-- Header -->
        <rect x="0" y="0" width="530" height="60" fill="rgba(255,255,255,0.05)" rx="10"/>
        <text x="20" y="25" fill="white" font-size="16" font-weight="bold">Tableau de Bord</text>
        <text x="20" y="45" fill="rgba(255,255,255,0.7)" font-size="12">Bienvenue, Thomas M.</text>
        
        <!-- Live indicator -->
        <circle cx="480" cy="30" r="6" fill="#43e97b">
          <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
        </circle>
        <text x="495" y="35" fill="#43e97b" font-size="12" font-weight="bold">LIVE</text>
        
        <!-- Stats cards row -->
        <g transform="translate(0, 80)">
          <!-- Portfolio value -->
          <rect x="0" y="0" width="120" height="80" rx="10" fill="url(#dashMockGrad1)" opacity="0.8" filter="url(#dashMockGlow)"/>
          <text x="60" y="20" text-anchor="middle" fill="white" font-size="10">Portfolio Total</text>
          <text x="60" y="40" text-anchor="middle" fill="white" font-size="18" font-weight="bold">€24,567</text>
          <text x="60" y="60" text-anchor="middle" fill="#43e97b" font-size="12">+12.8% ↗</text>
          
          <!-- Daily profit -->
          <rect x="140" y="0" width="120" height="80" rx="10" fill="url(#dashMockGrad3)" opacity="0.8" filter="url(#dashMockGlow)"/>
          <text x="200" y="20" text-anchor="middle" fill="white" font-size="10">Profit 24h</text>
          <text x="200" y="40" text-anchor="middle" fill="white" font-size="18" font-weight="bold">€1,247</text>
          <text x="200" y="60" text-anchor="middle" fill="#43e97b" font-size="12">+8.4% ↗</text>
          
          <!-- Active bots -->
          <rect x="280" y="0" width="120" height="80" rx="10" fill="url(#dashMockGrad2)" opacity="0.8" filter="url(#dashMockGlow)"/>
          <text x="340" y="20" text-anchor="middle" fill="white" font-size="10">Bots Actifs</text>
          <text x="340" y="40" text-anchor="middle" fill="white" font-size="18" font-weight="bold">3</text>
          <text x="340" y="60" text-anchor="middle" fill="#43e97b" font-size="12">En cours</text>
          
          <!-- Win rate -->
          <rect x="420" y="0" width="110" height="80" rx="10" fill="url(#dashMockGrad4)" opacity="0.8" filter="url(#dashMockGlow)"/>
          <text x="475" y="20" text-anchor="middle" fill="white" font-size="10">Taux Réussite</text>
          <text x="475" y="40" text-anchor="middle" fill="white" font-size="18" font-weight="bold">87%</text>
          <text x="475" y="60" text-anchor="middle" fill="#43e97b" font-size="12">Excellent</text>
        </g>
        
        <!-- Trading chart section -->
        <g transform="translate(0, 180)">
          <rect x="0" y="0" width="530" height="200" rx="10" fill="rgba(255,255,255,0.05)"/>
          <text x="20" y="25" fill="white" font-size="14" font-weight="bold">Performance en Temps Réel</text>
          <text x="20" y="45" fill="rgba(255,255,255,0.7)" font-size="12">BTC/USDT • ETH/USDT • ADA/USDT</text>
          
          <!-- Chart grid -->
          <g stroke="rgba(255,255,255,0.1)" stroke-width="1">
            <line x1="40" y1="60" x2="510" y2="60"/>
            <line x1="40" y1="90" x2="510" y2="90"/>
            <line x1="40" y1="120" x2="510" y2="120"/>
            <line x1="40" y1="150" x2="510" y2="150"/>
            <line x1="40" y1="180" x2="510" y2="180"/>
          </g>
          
          <!-- Performance line -->
          <polyline points="40,160 80,150 120,130 160,125 200,110 240,105 280,95 320,100 360,85 400,80 440,90 480,75 510,70" 
                    stroke="url(#dashMockGrad3)" stroke-width="3" fill="none" filter="url(#dashMockGlow)"/>
          
          <!-- Area under curve -->
          <polygon points="40,180 80,150 120,130 160,125 200,110 240,105 280,95 320,100 360,85 400,80 440,90 480,75 510,70 510,180" 
                   fill="url(#dashMockGrad3)" opacity="0.2"/>
          
          <!-- Data points -->
          <circle cx="200" cy="110" r="3" fill="#43e97b" filter="url(#dashMockGlow)">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
          </circle>
          <circle cx="320" cy="100" r="3" fill="#43e97b" filter="url(#dashMockGlow)">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="0.5s"/>
          </circle>
          <circle cx="440" cy="90" r="3" fill="#43e97b" filter="url(#dashMockGlow)">
            <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="1s"/>
          </circle>
          
          <!-- Trading signals -->
          <g transform="translate(420, 60)">
            <rect x="0" y="0" width="80" height="20" rx="10" fill="#43e97b" opacity="0.8"/>
            <text x="40" y="13" text-anchor="middle" fill="white" font-size="10" font-weight="bold">BUY SIGNAL</text>
          </g>
          
          <g transform="translate(420, 90)">
            <rect x="0" y="0" width="80" height="20" rx="10" fill="#f5576c" opacity="0.8"/>
            <text x="40" y="13" text-anchor="middle" fill="white" font-size="10" font-weight="bold">SELL SIGNAL</text>
          </g>
        </g>
      </g>
    </g>
  </g>
  
  <!-- Floating elements -->
  <circle cx="100" cy="100" r="3" fill="url(#dashMockGrad1)" opacity="0.6">
    <animate attributeName="cy" values="100;80;100" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="700" cy="150" r="2" fill="url(#dashMockGrad2)" opacity="0.6">
    <animate attributeName="cy" values="150;130;150" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="750" cy="400" r="2" fill="url(#dashMockGrad3)" opacity="0.6">
    <animate attributeName="cy" values="400;380;400" dur="6s" repeatCount="indefinite"/>
  </circle>
</svg>
