<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="aiGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <filter id="aiGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="rgba(10, 14, 39, 0.1)" rx="20"/>
  
  <!-- Central AI Brain -->
  <g transform="translate(200, 150)">
    <!-- Outer ring -->
    <circle cx="0" cy="0" r="60" fill="none" stroke="url(#aiGrad1)" stroke-width="3" opacity="0.6">
      <animate attributeName="r" values="60;70;60" dur="4s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Middle ring -->
    <circle cx="0" cy="0" r="40" fill="none" stroke="url(#aiGrad2)" stroke-width="2" opacity="0.8">
      <animate attributeName="r" values="40;50;40" dur="3s" repeatCount="indefinite"/>
    </circle>
    
    <!-- Core -->
    <circle cx="0" cy="0" r="25" fill="url(#aiGrad1)" opacity="0.8" filter="url(#aiGlow)"/>
    
    <!-- AI Text -->
    <text x="0" y="8" text-anchor="middle" fill="white" font-size="18" font-weight="bold">AI</text>
  </g>
  
  <!-- Neural Network Nodes -->
  <g fill="url(#aiGrad1)" opacity="0.7">
    <!-- Layer 1 -->
    <circle cx="80" cy="80" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="150" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.3s"/>
    </circle>
    <circle cx="80" cy="220" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
    
    <!-- Layer 2 -->
    <circle cx="320" cy="80" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>
    <circle cx="320" cy="150" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1.3s"/>
    </circle>
    <circle cx="320" cy="220" r="8">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite" begin="1.6s"/>
    </circle>
  </g>
  
  <!-- Neural Connections -->
  <g stroke="url(#aiGrad1)" stroke-width="2" opacity="0.4">
    <!-- From left nodes to center -->
    <line x1="88" y1="80" x2="175" y2="130">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite"/>
    </line>
    <line x1="88" y1="150" x2="175" y2="150">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="0.5s"/>
    </line>
    <line x1="88" y1="220" x2="175" y2="170">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="1s"/>
    </line>
    
    <!-- From center to right nodes -->
    <line x1="225" y1="130" x2="312" y2="80">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="1.5s"/>
    </line>
    <line x1="225" y1="150" x2="312" y2="150">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="2s"/>
    </line>
    <line x1="225" y1="170" x2="312" y2="220">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="3s" repeatCount="indefinite" begin="2.5s"/>
    </line>
  </g>
  
  <!-- Data Processing Indicators -->
  <g transform="translate(50, 50)">
    <rect x="0" y="0" width="30" height="4" rx="2" fill="url(#aiGrad2)" opacity="0.8">
      <animate attributeName="width" values="30;5;30" dur="2s" repeatCount="indefinite"/>
    </rect>
    <rect x="0" y="8" width="25" height="4" rx="2" fill="url(#aiGrad2)" opacity="0.6">
      <animate attributeName="width" values="25;8;25" dur="2.5s" repeatCount="indefinite"/>
    </rect>
    <rect x="0" y="16" width="35" height="4" rx="2" fill="url(#aiGrad2)" opacity="0.7">
      <animate attributeName="width" values="35;10;35" dur="1.8s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <g transform="translate(320, 50)">
    <rect x="0" y="0" width="30" height="4" rx="2" fill="url(#aiGrad1)" opacity="0.8">
      <animate attributeName="width" values="30;5;30" dur="2.2s" repeatCount="indefinite"/>
    </rect>
    <rect x="0" y="8" width="25" height="4" rx="2" fill="url(#aiGrad1)" opacity="0.6">
      <animate attributeName="width" values="25;8;25" dur="2.7s" repeatCount="indefinite"/>
    </rect>
    <rect x="0" y="16" width="35" height="4" rx="2" fill="url(#aiGrad1)" opacity="0.7">
      <animate attributeName="width" values="35;10;35" dur="2s" repeatCount="indefinite"/>
    </rect>
  </g>
  
  <!-- Floating Algorithm Symbols -->
  <g fill="url(#aiGrad1)" opacity="0.6" font-size="12" font-weight="bold">
    <text x="150" y="50">∑</text>
    <text x="250" y="60">∆</text>
    <text x="120" y="280">∞</text>
    <text x="280" y="270">π</text>
    
    <animateTransform attributeName="transform" type="translate" values="0,0; 0,-5; 0,0" dur="3s" repeatCount="indefinite"/>
  </g>
  
  <!-- Processing waves -->
  <g stroke="url(#aiGrad2)" stroke-width="1" fill="none" opacity="0.3">
    <path d="M50,250 Q100,240 150,250 T250,250 T350,250">
      <animate attributeName="d" values="M50,250 Q100,240 150,250 T250,250 T350,250;M50,250 Q100,260 150,250 T250,250 T350,250;M50,250 Q100,240 150,250 T250,250 T350,250" dur="4s" repeatCount="indefinite"/>
    </path>
  </g>
</svg>
