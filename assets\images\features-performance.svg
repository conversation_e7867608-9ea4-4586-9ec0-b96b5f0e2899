<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="perfGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="perfGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <filter id="perfGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="rgba(10, 14, 39, 0.1)" rx="20"/>
  
  <!-- Main Chart Container -->
  <g transform="translate(50, 50)">
    <rect x="0" y="0" width="300" height="200" rx="10" fill="rgba(26, 31, 58, 0.8)" stroke="url(#perfGrad1)" stroke-width="1"/>
    
    <!-- Chart Title -->
    <text x="150" y="25" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Performance Analytics</text>
    
    <!-- Grid Lines -->
    <g stroke="rgba(255,255,255,0.1)" stroke-width="1">
      <line x1="30" y1="50" x2="270" y2="50"/>
      <line x1="30" y1="80" x2="270" y2="80"/>
      <line x1="30" y1="110" x2="270" y2="110"/>
      <line x1="30" y1="140" x2="270" y2="140"/>
      <line x1="30" y1="170" x2="270" y2="170"/>
      
      <line x1="30" y1="50" x2="30" y2="170"/>
      <line x1="90" y1="50" x2="90" y2="170"/>
      <line x1="150" y1="50" x2="150" y2="170"/>
      <line x1="210" y1="50" x2="210" y2="170"/>
      <line x1="270" y1="50" x2="270" y2="170"/>
    </g>
    
    <!-- Performance Line -->
    <polyline points="30,150 60,140 90,120 120,110 150,90 180,85 210,70 240,65 270,55" 
              stroke="url(#perfGrad1)" stroke-width="4" fill="none" filter="url(#perfGlow)"/>
    
    <!-- Area under curve -->
    <polygon points="30,170 60,140 90,120 120,110 150,90 180,85 210,70 240,65 270,55 270,170" 
             fill="url(#perfGrad1)" opacity="0.2"/>
    
    <!-- Data Points -->
    <circle cx="90" cy="120" r="4" fill="url(#perfGrad1)" filter="url(#perfGlow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="150" cy="90" r="4" fill="url(#perfGrad1)" filter="url(#perfGlow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="210" cy="70" r="4" fill="url(#perfGrad1)" filter="url(#perfGlow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>
    
    <!-- Y-axis labels -->
    <text x="25" y="55" text-anchor="end" fill="rgba(255,255,255,0.7)" font-size="10">100%</text>
    <text x="25" y="85" text-anchor="end" fill="rgba(255,255,255,0.7)" font-size="10">75%</text>
    <text x="25" y="115" text-anchor="end" fill="rgba(255,255,255,0.7)" font-size="10">50%</text>
    <text x="25" y="145" text-anchor="end" fill="rgba(255,255,255,0.7)" font-size="10">25%</text>
    <text x="25" y="175" text-anchor="end" fill="rgba(255,255,255,0.7)" font-size="10">0%</text>
    
    <!-- X-axis labels -->
    <text x="30" y="190" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="10">Jan</text>
    <text x="90" y="190" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="10">Mar</text>
    <text x="150" y="190" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="10">May</text>
    <text x="210" y="190" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="10">Jul</text>
    <text x="270" y="190" text-anchor="middle" fill="rgba(255,255,255,0.7)" font-size="10">Sep</text>
  </g>
  
  <!-- Performance Stats -->
  <g transform="translate(20, 20)">
    <!-- ROI Box -->
    <rect x="0" y="0" width="80" height="25" rx="5" fill="url(#perfGrad1)" opacity="0.8"/>
    <text x="40" y="17" text-anchor="middle" fill="white" font-size="12" font-weight="bold">+24.7% ROI</text>
  </g>
  
  <g transform="translate(320, 20)">
    <!-- Win Rate Box -->
    <rect x="0" y="0" width="70" height="25" rx="5" fill="url(#perfGrad2)" opacity="0.8"/>
    <text x="35" y="17" text-anchor="middle" fill="white" font-size="12" font-weight="bold">87% Win</text>
  </g>
  
  <!-- Trending Arrow -->
  <g transform="translate(350, 100)">
    <path d="M0,20 L20,0 L15,5 M20,0 L15,-5" stroke="url(#perfGrad1)" stroke-width="3" fill="none" filter="url(#perfGlow)">
      <animate attributeName="opacity" values="0.7;1;0.7" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- Profit Indicators -->
  <g transform="translate(30, 270)">
    <circle cx="0" cy="0" r="8" fill="url(#perfGrad1)" opacity="0.8"/>
    <text x="0" y="3" text-anchor="middle" fill="white" font-size="10" font-weight="bold">$</text>
    <text x="20" y="5" fill="white" font-size="12">+$2,847</text>
  </g>
  
  <g transform="translate(150, 270)">
    <circle cx="0" cy="0" r="8" fill="url(#perfGrad2)" opacity="0.8"/>
    <text x="0" y="3" text-anchor="middle" fill="white" font-size="10" font-weight="bold">%</text>
    <text x="20" y="5" fill="white" font-size="12">+15.2%</text>
  </g>
  
  <g transform="translate(270, 270)">
    <circle cx="0" cy="0" r="8" fill="url(#perfGrad1)" opacity="0.8"/>
    <text x="0" y="3" text-anchor="middle" fill="white" font-size="10" font-weight="bold">⚡</text>
    <text x="20" y="5" fill="white" font-size="12">24/7</text>
  </g>
  
  <!-- Floating Success Particles -->
  <circle cx="100" cy="80" r="2" fill="url(#perfGrad1)" opacity="0.6">
    <animate attributeName="cy" values="80;60;80" dur="3s" repeatCount="indefinite"/>
  </circle>
  <circle cx="300" cy="120" r="2" fill="url(#perfGrad2)" opacity="0.6">
    <animate attributeName="cy" values="120;100;120" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="200" cy="200" r="2" fill="url(#perfGrad1)" opacity="0.6">
    <animate attributeName="cy" values="200;180;200" dur="5s" repeatCount="indefinite"/>
  </circle>
</svg>
