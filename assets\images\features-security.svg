<svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="secGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    <filter id="secGlow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="rgba(10, 14, 39, 0.1)" rx="20"/>
  
  <!-- Main shield -->
  <g transform="translate(200, 150)">
    <path d="M0,-80 L-40,-40 L-40,20 Q-40,40 -20,60 Q0,80 0,80 Q0,80 20,60 Q40,40 40,20 L40,-40 Z" 
          fill="url(#secGrad1)" filter="url(#secGlow)"/>
    
    <!-- Inner shield -->
    <path d="M0,-60 L-30,-30 L-30,15 Q-30,30 -15,45 Q0,60 0,60 Q0,60 15,45 Q30,30 30,15 L30,-30 Z" 
          fill="url(#secGrad2)" opacity="0.8"/>
    
    <!-- Check mark -->
    <path d="M-15,0 L-5,10 L15,-10" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
    
    <!-- Pulse animation -->
    <circle cx="0" cy="0" r="90" fill="none" stroke="url(#secGrad1)" stroke-width="2" opacity="0.3">
      <animate attributeName="r" values="90;110;90" dur="3s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0.3;0.1;0.3" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Lock icons around shield -->
  <g transform="translate(120, 80)">
    <rect x="-8" y="-5" width="16" height="12" rx="2" fill="url(#secGrad2)" opacity="0.8"/>
    <path d="M-6,-5 Q-6,-12 0,-12 Q6,-12 6,-5" stroke="url(#secGrad2)" stroke-width="2" fill="none"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite"/>
  </g>
  
  <g transform="translate(280, 80)">
    <rect x="-8" y="-5" width="16" height="12" rx="2" fill="url(#secGrad2)" opacity="0.8"/>
    <path d="M-6,-5 Q-6,-12 0,-12 Q6,-12 6,-5" stroke="url(#secGrad2)" stroke-width="2" fill="none"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" begin="0.5s"/>
  </g>
  
  <g transform="translate(120, 220)">
    <rect x="-8" y="-5" width="16" height="12" rx="2" fill="url(#secGrad2)" opacity="0.8"/>
    <path d="M-6,-5 Q-6,-12 0,-12 Q6,-12 6,-5" stroke="url(#secGrad2)" stroke-width="2" fill="none"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" begin="1s"/>
  </g>
  
  <g transform="translate(280, 220)">
    <rect x="-8" y="-5" width="16" height="12" rx="2" fill="url(#secGrad2)" opacity="0.8"/>
    <path d="M-6,-5 Q-6,-12 0,-12 Q6,-12 6,-5" stroke="url(#secGrad2)" stroke-width="2" fill="none"/>
    <animate attributeName="opacity" values="0.8;1;0.8" dur="2s" repeatCount="indefinite" begin="1.5s"/>
  </g>
  
  <!-- Data flow lines -->
  <g stroke="url(#secGrad1)" stroke-width="2" opacity="0.4">
    <line x1="50" y1="150" x2="150" y2="150">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite"/>
    </line>
    <line x1="250" y1="150" x2="350" y2="150">
      <animate attributeName="opacity" values="0.4;0.8;0.4" dur="2s" repeatCount="indefinite" begin="1s"/>
    </line>
  </g>
  
  <!-- Floating particles -->
  <circle cx="80" cy="100" r="2" fill="url(#secGrad1)" opacity="0.6">
    <animate attributeName="cy" values="100;80;100" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="200" r="2" fill="url(#secGrad2)" opacity="0.6">
    <animate attributeName="cy" values="200;180;200" dur="5s" repeatCount="indefinite"/>
  </circle>
</svg>
