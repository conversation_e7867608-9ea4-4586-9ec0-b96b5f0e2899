<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="grad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circles -->
  <circle cx="150" cy="100" r="80" fill="url(#grad1)" opacity="0.3"/>
  <circle cx="450" cy="150" r="60" fill="url(#grad2)" opacity="0.3"/>
  <circle cx="300" cy="300" r="70" fill="url(#grad3)" opacity="0.3"/>
  
  <!-- Main crypto symbols -->
  <g transform="translate(200, 80)">
    <!-- Bitcoin -->
    <circle cx="0" cy="0" r="40" fill="url(#grad1)" opacity="0.9"/>
    <text x="0" y="8" text-anchor="middle" fill="white" font-size="24" font-weight="bold">₿</text>
  </g>
  
  <g transform="translate(350, 120)">
    <!-- Ethereum -->
    <circle cx="0" cy="0" r="35" fill="url(#grad2)" opacity="0.9"/>
    <text x="0" y="8" text-anchor="middle" fill="white" font-size="20" font-weight="bold">Ξ</text>
  </g>
  
  <g transform="translate(280, 220)">
    <!-- Generic crypto -->
    <circle cx="0" cy="0" r="30" fill="url(#grad3)" opacity="0.9"/>
    <text x="0" y="8" text-anchor="middle" fill="white" font-size="18" font-weight="bold">$</text>
  </g>
  
  <!-- Connecting lines -->
  <line x1="200" y1="80" x2="350" y2="120" stroke="url(#grad1)" stroke-width="2" opacity="0.5"/>
  <line x1="350" y1="120" x2="280" y2="220" stroke="url(#grad2)" stroke-width="2" opacity="0.5"/>
  <line x1="280" y1="220" x2="200" y2="80" stroke="url(#grad3)" stroke-width="2" opacity="0.5"/>
  
  <!-- Floating elements -->
  <g opacity="0.6">
    <circle cx="100" cy="200" r="3" fill="#667eea">
      <animate attributeName="cy" values="200;180;200" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="500" cy="250" r="4" fill="#f093fb">
      <animate attributeName="cy" values="250;230;250" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="80" cy="320" r="2" fill="#4facfe">
      <animate attributeName="cy" values="320;300;320" dur="5s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- Chart representation -->
  <g transform="translate(400, 250)">
    <rect x="0" y="0" width="150" height="100" fill="rgba(255,255,255,0.1)" rx="10"/>
    <polyline points="10,80 30,60 50,40 70,50 90,30 110,20 130,35" 
              stroke="url(#grad1)" stroke-width="3" fill="none"/>
    <text x="75" y="95" text-anchor="middle" fill="white" font-size="10">Trading Chart</text>
  </g>
</svg>
