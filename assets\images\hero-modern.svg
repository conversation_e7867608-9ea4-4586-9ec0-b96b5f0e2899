<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="modernGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4f46e5;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="modernGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="modernGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <filter id="modernShadow">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="600" height="400" fill="#f8fafc" rx="20"/>
  
  <!-- Main device mockup -->
  <g transform="translate(50, 50)">
    <!-- Device frame -->
    <rect x="0" y="0" width="500" height="300" rx="20" fill="white" stroke="#e2e8f0" stroke-width="2" filter="url(#modernShadow)"/>
    
    <!-- Screen -->
    <rect x="20" y="20" width="460" height="260" rx="12" fill="#0f172a"/>
    
    <!-- Header bar -->
    <rect x="20" y="20" width="460" height="40" rx="12" fill="url(#modernGrad1)"/>
    <text x="50" y="42" fill="white" font-size="14" font-weight="bold">CryptoBoost Dashboard</text>
    
    <!-- Status indicator -->
    <circle cx="430" cy="40" r="4" fill="#10b981">
      <animate attributeName="opacity" values="1;0.5;1" dur="2s" repeatCount="indefinite"/>
    </circle>
    <text x="445" y="44" fill="white" font-size="10">LIVE</text>
    
    <!-- Main content area -->
    <g transform="translate(40, 80)">
      <!-- Stats cards -->
      <rect x="0" y="0" width="100" height="60" rx="8" fill="url(#modernGrad1)" opacity="0.9"/>
      <text x="50" y="20" text-anchor="middle" fill="white" font-size="10">Portfolio</text>
      <text x="50" y="35" text-anchor="middle" fill="white" font-size="16" font-weight="bold">€24,567</text>
      <text x="50" y="50" text-anchor="middle" fill="#10b981" font-size="10">+12.8%</text>
      
      <rect x="120" y="0" width="100" height="60" rx="8" fill="url(#modernGrad2)" opacity="0.9"/>
      <text x="170" y="20" text-anchor="middle" fill="white" font-size="10">Profit 24h</text>
      <text x="170" y="35" text-anchor="middle" fill="white" font-size="16" font-weight="bold">€1,247</text>
      <text x="170" y="50" text-anchor="middle" fill="#10b981" font-size="10">+8.4%</text>
      
      <rect x="240" y="0" width="100" height="60" rx="8" fill="url(#modernGrad3)" opacity="0.9"/>
      <text x="290" y="20" text-anchor="middle" fill="white" font-size="10">Bots Actifs</text>
      <text x="290" y="35" text-anchor="middle" fill="white" font-size="16" font-weight="bold">3</text>
      <text x="290" y="50" text-anchor="middle" fill="#10b981" font-size="10">Running</text>
      
      <rect x="360" y="0" width="80" height="60" rx="8" fill="rgba(255,255,255,0.1)"/>
      <text x="400" y="20" text-anchor="middle" fill="white" font-size="10">Win Rate</text>
      <text x="400" y="35" text-anchor="middle" fill="white" font-size="16" font-weight="bold">87%</text>
      <text x="400" y="50" text-anchor="middle" fill="#10b981" font-size="10">Excellent</text>
      
      <!-- Chart area -->
      <rect x="0" y="80" width="420" height="120" rx="8" fill="rgba(255,255,255,0.05)"/>
      <text x="20" y="100" fill="white" font-size="12" font-weight="bold">Performance Chart</text>
      
      <!-- Simple chart line -->
      <polyline points="20,180 60,170 100,160 140,155 180,145 220,140 260,135 300,140 340,130 380,125 420,120" 
                stroke="url(#modernGrad2)" stroke-width="3" fill="none"/>
      
      <!-- Chart area fill -->
      <polygon points="20,190 60,170 100,160 140,155 180,145 220,140 260,135 300,140 340,130 380,125 420,120 420,190" 
               fill="url(#modernGrad2)" opacity="0.2"/>
      
      <!-- Data points -->
      <circle cx="180" cy="145" r="3" fill="#10b981">
        <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="300" cy="140" r="3" fill="#10b981">
        <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="0.5s"/>
      </circle>
      <circle cx="420" cy="120" r="3" fill="#10b981">
        <animate attributeName="r" values="3;5;3" dur="2s" repeatCount="indefinite" begin="1s"/>
      </circle>
    </g>
  </g>
  
  <!-- Floating elements -->
  <g opacity="0.6">
    <!-- Bitcoin symbol -->
    <circle cx="100" cy="100" r="20" fill="url(#modernGrad3)" opacity="0.8">
      <animate attributeName="cy" values="100;90;100" dur="4s" repeatCount="indefinite"/>
    </circle>
    <text x="100" y="107" text-anchor="middle" fill="white" font-size="16" font-weight="bold">₿</text>
    
    <!-- Ethereum symbol -->
    <circle cx="520" cy="120" r="15" fill="url(#modernGrad1)" opacity="0.8">
      <animate attributeName="cy" values="120;110;120" dur="5s" repeatCount="indefinite"/>
    </circle>
    <text x="520" y="127" text-anchor="middle" fill="white" font-size="12" font-weight="bold">Ξ</text>
    
    <!-- Arrow up -->
    <g transform="translate(480, 300)">
      <circle cx="0" cy="0" r="12" fill="url(#modernGrad2)" opacity="0.8">
        <animate attributeName="cy" values="0;-10;0" dur="3s" repeatCount="indefinite"/>
      </circle>
      <path d="M-4,2 L0,-4 L4,2 M0,-4 L0,4" stroke="white" stroke-width="2" fill="none"/>
    </g>
  </g>
  
  <!-- Subtle grid pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e2e8f0" stroke-width="0.5" opacity="0.3"/>
    </pattern>
  </defs>
  <rect width="600" height="400" fill="url(#grid)" opacity="0.5"/>
</svg>
