<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGrad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heroGrad2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heroGrad3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heroGrad4" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#43e97b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38f9d7;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="800" height="600" fill="url(#heroGrad1)" opacity="0.1"/>
  
  <!-- Floating background elements -->
  <circle cx="100" cy="100" r="60" fill="url(#heroGrad1)" opacity="0.2">
    <animate attributeName="cy" values="100;80;100" dur="4s" repeatCount="indefinite"/>
  </circle>
  <circle cx="700" cy="150" r="40" fill="url(#heroGrad2)" opacity="0.2">
    <animate attributeName="cy" values="150;130;150" dur="5s" repeatCount="indefinite"/>
  </circle>
  <circle cx="650" cy="450" r="50" fill="url(#heroGrad3)" opacity="0.2">
    <animate attributeName="cy" values="450;430;450" dur="6s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Main trading interface mockup -->
  <g transform="translate(150, 80)">
    <!-- Main dashboard container -->
    <rect x="0" y="0" width="500" height="400" rx="20" fill="rgba(10, 14, 39, 0.9)" stroke="url(#heroGrad1)" stroke-width="2" filter="url(#glow)"/>
    
    <!-- Header bar -->
    <rect x="20" y="20" width="460" height="50" rx="10" fill="rgba(26, 31, 58, 0.8)"/>
    <text x="40" y="45" fill="white" font-size="16" font-weight="bold">CryptoBoost Dashboard</text>
    <circle cx="440" cy="45" r="8" fill="#43e97b"/>
    <text x="460" y="50" fill="#43e97b" font-size="12">LIVE</text>
    
    <!-- Stats cards -->
    <rect x="20" y="90" width="140" height="80" rx="10" fill="url(#heroGrad1)" opacity="0.8"/>
    <text x="90" y="115" text-anchor="middle" fill="white" font-size="12">Portfolio</text>
    <text x="90" y="135" text-anchor="middle" fill="white" font-size="20" font-weight="bold">$24,567</text>
    <text x="90" y="155" text-anchor="middle" fill="#43e97b" font-size="14">+15.2%</text>
    
    <rect x="180" y="90" width="140" height="80" rx="10" fill="url(#heroGrad2)" opacity="0.8"/>
    <text x="250" y="115" text-anchor="middle" fill="white" font-size="12">Profit 24h</text>
    <text x="250" y="135" text-anchor="middle" fill="white" font-size="20" font-weight="bold">$1,247</text>
    <text x="250" y="155" text-anchor="middle" fill="#43e97b" font-size="14">+8.4%</text>
    
    <rect x="340" y="90" width="140" height="80" rx="10" fill="url(#heroGrad3)" opacity="0.8"/>
    <text x="410" y="115" text-anchor="middle" fill="white" font-size="12">Active Bots</text>
    <text x="410" y="135" text-anchor="middle" fill="white" font-size="20" font-weight="bold">3</text>
    <text x="410" y="155" text-anchor="middle" fill="#43e97b" font-size="14">Running</text>
    
    <!-- Trading chart -->
    <rect x="20" y="190" width="460" height="180" rx="10" fill="rgba(255,255,255,0.05)"/>
    <text x="40" y="215" fill="white" font-size="14" font-weight="bold">BTC/USDT - Live Trading</text>
    
    <!-- Chart grid -->
    <g stroke="rgba(255,255,255,0.1)" stroke-width="1">
      <line x1="40" y1="230" x2="460" y2="230"/>
      <line x1="40" y1="260" x2="460" y2="260"/>
      <line x1="40" y1="290" x2="460" y2="290"/>
      <line x1="40" y1="320" x2="460" y2="320"/>
      <line x1="40" y1="350" x2="460" y2="350"/>
    </g>
    
    <!-- Price line -->
    <polyline points="40,320 80,300 120,280 160,290 200,260 240,250 280,240 320,245 360,230 400,225 440,235 460,220" 
              stroke="url(#heroGrad4)" stroke-width="3" fill="none" filter="url(#glow)"/>
    
    <!-- Price points -->
    <circle cx="120" cy="280" r="4" fill="#43e97b" filter="url(#glow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="240" cy="250" r="4" fill="#43e97b" filter="url(#glow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" begin="0.5s"/>
    </circle>
    <circle cx="360" cy="230" r="4" fill="#43e97b" filter="url(#glow)">
      <animate attributeName="r" values="4;6;4" dur="2s" repeatCount="indefinite" begin="1s"/>
    </circle>
    
    <!-- Buy/Sell indicators -->
    <g transform="translate(380, 280)">
      <rect x="0" y="0" width="80" height="25" rx="12" fill="#43e97b" opacity="0.8"/>
      <text x="40" y="17" text-anchor="middle" fill="white" font-size="12" font-weight="bold">BUY</text>
    </g>
    
    <g transform="translate(380, 315)">
      <rect x="0" y="0" width="80" height="25" rx="12" fill="#f5576c" opacity="0.8"/>
      <text x="40" y="17" text-anchor="middle" fill="white" font-size="12" font-weight="bold">SELL</text>
    </g>
  </g>
  
  <!-- Floating crypto symbols -->
  <g transform="translate(50, 300)">
    <circle cx="0" cy="0" r="25" fill="url(#heroGrad1)" opacity="0.8">
      <animate attributeName="cy" values="0;-10;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="6" text-anchor="middle" fill="white" font-size="16" font-weight="bold">₿</text>
  </g>
  
  <g transform="translate(720, 350)">
    <circle cx="0" cy="0" r="20" fill="url(#heroGrad2)" opacity="0.8">
      <animate attributeName="cy" values="0;-8;0" dur="4s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="5" text-anchor="middle" fill="white" font-size="14" font-weight="bold">Ξ</text>
  </g>
  
  <g transform="translate(80, 500)">
    <circle cx="0" cy="0" r="18" fill="url(#heroGrad3)" opacity="0.8">
      <animate attributeName="cy" values="0;-6;0" dur="5s" repeatCount="indefinite"/>
    </circle>
    <text x="0" y="5" text-anchor="middle" fill="white" font-size="12" font-weight="bold">$</text>
  </g>
  
  <!-- AI Brain illustration -->
  <g transform="translate(680, 80)">
    <circle cx="0" cy="0" r="35" fill="url(#heroGrad4)" opacity="0.3"/>
    <circle cx="0" cy="0" r="25" fill="url(#heroGrad4)" opacity="0.6"/>
    <circle cx="0" cy="0" r="15" fill="url(#heroGrad4)" opacity="0.9"/>
    <text x="0" y="5" text-anchor="middle" fill="white" font-size="16" font-weight="bold">AI</text>
    
    <!-- Neural network connections -->
    <g stroke="url(#heroGrad4)" stroke-width="2" opacity="0.6">
      <line x1="-20" y1="-20" x2="20" y2="20">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite"/>
      </line>
      <line x1="20" y1="-20" x2="-20" y2="20">
        <animate attributeName="opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" begin="1s"/>
      </line>
    </g>
  </g>
</svg>
