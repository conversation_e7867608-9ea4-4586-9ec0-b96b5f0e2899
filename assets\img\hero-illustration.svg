<svg viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="glow-grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00f2ff; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8e2de2; stop-opacity:1" />
    </linearGradient>
    <filter id="glow-filter">
      <feGaussianBlur stdDeviation="4.5" result="coloredBlur" />
      <feMerge>
        <feMergeNode in="coloredBlur" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  <g filter="url(#glow-filter)">
    <!-- Abstract network lines -->
    <path d="M 50 150 Q 150 50 250 150 T 350 150" stroke="url(#glow-grad)" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 50 150 Q 150 250 250 150" stroke="url(#glow-grad)" stroke-width="2" fill="none" opacity="0.6"/>
    <path d="M 100 100 Q 200 200 300 100" stroke="url(#glow-grad)" stroke-width="1.5" fill="none" opacity="0.4"/>

    <!-- Central Node (AI Brain) -->
    <circle cx="200" cy="150" r="20" fill="none" stroke="#fff" stroke-width="2.5"/>
    <circle cx="200" cy="150" r="15" fill="url(#glow-grad)" opacity="0.7"/>

    <!-- Data points/coins -->
    <circle cx="80" cy="120" r="8" fill="#00f2ff"/>
    <circle cx="320" cy="180" r="10" fill="#8e2de2"/>
    <circle cx="150" cy="220" r="6" fill="#fff"/>
    <circle cx="250" cy="80" r="7" fill="#00f2ff"/>
  </g>
</svg>
