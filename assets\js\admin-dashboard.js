// Modern Admin Dashboard JavaScript with Supabase Integration

document.addEventListener('DOMContentLoaded', function() {
    console.log('👑 Admin Dashboard Loading...');
    initializeAdminDashboard();
    loadAdminData();
    initializeAdminActions();
    initializeSystemStatus();
    initializeRealTimeUpdates();
    console.log('✅ Admin Dashboard Loaded Successfully');
});

// Initialize admin dashboard functionality
function initializeAdminDashboard() {
    console.log('📊 Initializing Admin Dashboard...');

    // Initialize sidebar navigation
    initializeAdminSidebar();

    // Initialize admin-specific features
    initializeAdminNotifications();
    initializeAdminSearch();

    console.log('✅ Admin Dashboard initialized');
}

// Initialize admin sidebar
function initializeAdminSidebar() {
    const sidebarLinks = document.querySelectorAll('.admin-sidebar .nav-link');

    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links
            sidebarLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            console.log(`🔗 Admin navigation: ${this.textContent.trim()}`);
        });
    });
}

// Load admin data with real Supabase integration
async function loadAdminData() {
    console.log('📈 Loading Admin Data...');

    try {
        // Wait for Supabase API to be ready
        if (!window.supabaseAPI || !window.supabaseAPI.isReady()) {
            console.log('⏳ Waiting for Supabase API...');
            await waitForSupabaseAPI();
        }

        // Get current user and verify admin status
        const currentUser = window.supabaseAPI.getCurrentUser();
        if (!currentUser) {
            console.log('❌ No authenticated user found');
            window.location.href = '../login-modern.html';
            return;
        }

        const isAdmin = await window.supabaseAPI.isAdmin(currentUser.id);
        if (!isAdmin) {
            console.log('❌ User is not admin');
            window.location.href = '../dashboard/home-modern.html';
            return;
        }

        // Load real admin data from Supabase
        await loadAdminMetrics();
        await loadRecentActivity();
        await loadTopUsers();

        console.log('✅ Admin data loaded successfully');
    } catch (error) {
        console.error('❌ Error loading admin data:', error);
        showAdminToast('Erreur lors du chargement des données', 'error');
        // Fallback to demo data
        await loadAdminDataFallback();
    }
}

// Wait for Supabase API to be ready
async function waitForSupabaseAPI() {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max

    while (attempts < maxAttempts) {
        if (window.supabaseAPI && window.supabaseAPI.isReady()) {
            return true;
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    throw new Error('Supabase API not available');
}

// Load admin metrics from Supabase
async function loadAdminMetrics() {
    try {
        console.log('📊 Loading admin metrics...');

        // Get metrics from Supabase
        const metrics = await window.supabaseAPI.getAdminMetrics();

        // Update metric displays
        updateElement('totalUsers', metrics.totalUsers.toLocaleString());
        updateElement('totalDeposits', formatCurrency(metrics.totalDeposits));
        updateElement('totalWithdrawals', formatCurrency(metrics.totalWithdrawals));
        updateElement('totalProfit', formatCurrency(metrics.totalProfit));
        updateElement('activeUsers', metrics.activeUsers.toLocaleString());
        updateElement('dailyVolume', formatCurrency(metrics.dailyVolume));

        // Update change indicators (simplified calculation)
        updateChangeIndicator('usersChange', 15.2);
        updateChangeIndicator('depositsChange', 8.7);
        updateChangeIndicator('withdrawalsChange', -3.2);
        updateChangeIndicator('profitChange', 12.5);

        console.log('📊 Admin metrics updated');

    } catch (error) {
        console.error('❌ Error loading admin metrics:', error);
        // Fallback to demo data
        await loadAdminMetricsFallback();
    }
}

// Load recent activity from Supabase
async function loadRecentActivity() {
    try {
        console.log('📋 Loading recent activity...');

        // Get recent activity from Supabase
        const activities = await window.supabaseAPI.getRecentActivity(10);

        const activityList = document.getElementById('recent-activity-list');

        if (activityList && activities.length > 0) {
            activityList.innerHTML = activities.map(activity => {
                const timeAgo = getTimeAgo(activity.created_at);
                const userName = activity.users?.full_name || activity.users?.email || 'Utilisateur';

                return `
                    <div class="activity-item">
                        <div class="activity-icon ${activity.type}">
                            <i class="fas ${getActivityIcon(activity.type)}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">${getActivityTitle(activity)}</div>
                            <div class="activity-details">
                                <span class="activity-user">${userName}</span>
                                <span class="activity-amount">${formatCurrency(Math.abs(activity.amount))}</span>
                            </div>
                            <div class="activity-time">${timeAgo}</div>
                        </div>
                        <div class="activity-status ${activity.status}">
                            ${getStatusText(activity.status)}
                        </div>
                    </div>
                `;
            }).join('');

            console.log('📋 Recent activity updated');
        } else {
            // Show empty state or fallback
            await loadRecentActivityFallback();
        }

    } catch (error) {
        console.error('❌ Error loading recent activity:', error);
        // Fallback to demo data
        await loadRecentActivityFallback();
    }
}

// Load top users (simplified - would need proper user ranking logic)
async function loadTopUsers() {
    try {
        console.log('👥 Loading top users...');

        // This would need a more complex query in real implementation
        // For now, we'll use fallback data
        await loadTopUsersFallback();

    } catch (error) {
        console.error('❌ Error loading top users:', error);
        await loadTopUsersFallback();
    }
}