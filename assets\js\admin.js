document.addEventListener('DOMContentLoaded', () => {
    const user = JSON.parse(localStorage.getItem('cryptoUser'));

    // Protect admin routes
    if (!user || user.role !== 'admin') {
        alert('Access denied. You must be an admin to view this page.');
        window.location.href = '../login.html';
        return;
    }

    // Basic router
    const path = window.location.pathname;
    if (path.includes('users.html')) {
        loadUsersPage();
    } else if (path.includes('deposits.html')) {
        loadDepositsPage();
    } else if (path.includes('withdrawals.html')) {
        loadWithdrawalsPage();
    } else if (path.includes('plans.html')) {
        loadPlansPage();
    } else if (path.includes('wallets.html')) {
        loadWalletsPage();
    } else {
        // Load dashboard stats or other pages
    }

    // Logout
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', () => {
            localStorage.removeItem('cryptoUser');
            window.location.href = '../login.html';
        });
    }
});

async function loadUsersPage() {
    const tableBody = document.querySelector('#admin-table tbody');
    if (!tableBody) return;

    try {
        // Fetch all data, then filter for users (rows with a password)
        const allData = await sheetDB.get(); 
        const users = allData.filter(row => row.password);

        if (!users || users.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="4">No users found.</td></tr>';
            return;
        }

        tableBody.innerHTML = ''; // Clear placeholder
        users.forEach(user => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${user.email}</td>
                <td>${user.role}</td>
                <td><span class="status-badge status-${user.status.toLowerCase()}">${user.status}</span></td>
                <td class="action-btns">
                    <button class="${user.status === 'active' ? 'btn-reject' : 'btn-approve'}" data-email="${user.email}" data-status="${user.status === 'active' ? 'inactive' : 'active'}">
                        ${user.status === 'active' ? 'Deactivate' : 'Activate'}
                    </button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        // Add event listeners for action buttons
        document.querySelectorAll('#admin-table .action-btns button').forEach(button => {
            button.addEventListener('click', handleUserStatusChange);
        });

    } catch (error) {
        console.error('Failed to load users:', error);
        tableBody.innerHTML = '<tr><td colspan="4">Error loading users. Please try again.</td></tr>';
    }
}

async function handleUserStatusChange(event) {
    const button = event.target;
    const email = button.dataset.email;
    const newStatus = button.dataset.status;

    if (confirm(`Are you sure you want to set user ${email} to ${newStatus}?`)) {
        const condition = `email/${email}`;
        const data = { status: newStatus };

        const result = await sheetDB.patch(condition, data);
        if (result && result.updated) {
            alert('User status updated successfully!');
            loadUsersPage(); // Refresh the table
        } else {
            alert('Failed to update user status.');
        }
    }
}

async function loadDepositsPage() {
    const tableBody = document.querySelector('#admin-table tbody');
    if (!tableBody) return;

    try {
        const deposits = await sheetDB.get('?action=deposit&status_action=pending');
        tableBody.innerHTML = ''; // Clear placeholder

        if (!deposits || deposits.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6">No pending deposits found.</td></tr>';
            return;
        }

        deposits.forEach(deposit => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(deposit.timestamp).toLocaleDateString()}</td>
                <td>${deposit.email}</td>
                <td>${deposit.crypto}</td>
                <td>${deposit.amount}</td>
                <td><span class="status-badge status-pending">${deposit.status_action}</span></td>
                <td class="action-btns">
                    <button class="btn-approve" data-id="${deposit.id}">Approve</button>
                    <button class="btn-reject" data-id="${deposit.id}">Reject</button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        document.querySelectorAll('.action-btns button').forEach(button => {
            button.addEventListener('click', handleTransactionStatusChange);
        });

    } catch (error) {
        console.error('Failed to load deposits:', error);
        tableBody.innerHTML = '<tr><td colspan="6">Error loading deposits.</td></tr>';
    }
}

async function loadWithdrawalsPage() {
    const tableBody = document.querySelector('#admin-table tbody');
    if (!tableBody) return;

    try {
        const withdrawals = await sheetDB.get('?action=withdrawal&status_action=pending');
        tableBody.innerHTML = ''; // Clear placeholder

        if (!withdrawals || withdrawals.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="7">No pending withdrawals found.</td></tr>';
            return;
        }

        withdrawals.forEach(withdrawal => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(withdrawal.timestamp).toLocaleDateString()}</td>
                <td>${withdrawal.email}</td>
                <td>${withdrawal.crypto}</td>
                <td>${withdrawal.amount}</td>
                <td>${withdrawal.wallet_address}</td>
                <td><span class="status-badge status-pending">${withdrawal.status_action}</span></td>
                <td class="action-btns">
                    <button class="btn-approve" data-id="${withdrawal.id}">Approve</button>
                    <button class="btn-reject" data-id="${withdrawal.id}">Reject</button>
                </td>
            `;
            tableBody.appendChild(row);
        });

        document.querySelectorAll('.action-btns button').forEach(button => {
            button.addEventListener('click', handleTransactionStatusChange);
        });

    } catch (error) {
        console.error('Failed to load withdrawals:', error);
        tableBody.innerHTML = '<tr><td colspan="7">Error loading withdrawals.</td></tr>';
    }
}

async function handleTransactionStatusChange(event) {
    const button = event.target;
    const transactionId = button.dataset.id;
    const newStatus = button.classList.contains('btn-approve') ? 'approved' : 'rejected';

    if (confirm(`Are you sure you want to ${newStatus} this transaction?`)) {
        const condition = `id/${transactionId}`;
        const data = { status_action: newStatus };

        const result = await sheetDB.patch(condition, data);
        if (result && result.updated) {
            alert('Transaction status updated successfully!');
            if (window.location.pathname.includes('deposits.html')) {
                loadDepositsPage();
            } else if (window.location.pathname.includes('withdrawals.html')) {
                loadWithdrawalsPage();
            }
        } else {
            alert('Failed to update transaction status.');
        }
    }
}

async function loadPlansPage() {
    const tableBody = document.getElementById('plans-table-body');
    const form = document.getElementById('plan-form');
    if (!tableBody || !form) return;

    try {
        const plans = await sheetDB.get('?sheet=config&type=plan');
        tableBody.innerHTML = '';

        if (plans && plans.length > 0) {
            plans.forEach(plan => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${plan.name}</td>
                    <td>${plan.price}</td>
                    <td>${plan.roi}</td>
                    <td>${plan.duration}</td>
                    <td class="action-btns">
                        <button class="btn-approve btn-edit" data-plan='${JSON.stringify(plan)}'>Edit</button>
                        <button class="btn-reject btn-delete" data-name="${plan.name}">Delete</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        document.querySelectorAll('.btn-edit').forEach(btn => btn.addEventListener('click', handlePlanEdit));
        document.querySelectorAll('.btn-delete').forEach(btn => btn.addEventListener('click', handlePlanDelete));
        form.addEventListener('submit', handlePlanFormSubmit);
        document.getElementById('clear-form-btn').addEventListener('click', clearPlanForm);

    } catch (error) {
        console.error('Failed to load plans:', error);
        tableBody.innerHTML = '<tr><td colspan="5">Error loading plans.</td></tr>';
    }
}

function handlePlanEdit(event) {
    const plan = JSON.parse(event.target.dataset.plan);
    document.getElementById('plan-form-title').innerText = 'Edit Plan';
    document.getElementById('plan-original-name').value = plan.name;
    document.getElementById('plan-name').value = plan.name;
    document.getElementById('plan-price').value = plan.price;
    document.getElementById('plan-roi').value = plan.roi;
    document.getElementById('plan-duration').value = plan.duration;
    window.scrollTo(0, document.body.scrollHeight);
}

function clearPlanForm() {
    document.getElementById('plan-form-title').innerText = 'Add New Plan';
    document.getElementById('plan-form').reset();
    document.getElementById('plan-original-name').value = '';
}

async function handlePlanFormSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const originalName = form.original_name.value;
    const planData = { type: 'plan', name: form.name.value, price: form.price.value, roi: form.roi.value, duration: form.duration.value };

    try {
        if (originalName) {
            await sheetDB.patch(`name/${originalName}?sheet=config`, planData);
            alert('Plan updated successfully!');
        } else {
            await sheetDB.post('?sheet=config', [planData]);
            alert('Plan created successfully!');
        }
        clearPlanForm();
        loadPlansPage();
    } catch (error) {
        console.error('Failed to save plan:', error);
        alert('Error saving plan.');
    }
}

async function handlePlanDelete(event) {
    const name = event.target.dataset.name;
    if (confirm(`Are you sure you want to delete the plan "${name}"?`)) {
        try {
            await sheetDB.delete(`name/${name}?sheet=config`);
            alert('Plan deleted successfully!');
            loadPlansPage();
        } catch (error) {
            console.error('Failed to delete plan:', error);
            alert('Error deleting plan.');
        }
    }
}

async function loadWalletsPage() {
    const tableBody = document.getElementById('wallets-table-body');
    const form = document.getElementById('wallet-form');
    if (!tableBody || !form) return;

    try {
        const wallets = await sheetDB.get('?sheet=config&type=wallet');
        tableBody.innerHTML = '';

        if (wallets && wallets.length > 0) {
            wallets.forEach(wallet => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${wallet.name}</td>
                    <td>${wallet.value}</td>
                    <td class="action-btns">
                        <button class="btn-approve btn-edit-wallet" data-wallet='${JSON.stringify(wallet)}'>Edit</button>
                        <button class="btn-reject btn-delete-wallet" data-name="${wallet.name}">Delete</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        document.querySelectorAll('.btn-edit-wallet').forEach(btn => btn.addEventListener('click', handleWalletEdit));
        document.querySelectorAll('.btn-delete-wallet').forEach(btn => btn.addEventListener('click', handleWalletDelete));
        form.addEventListener('submit', handleWalletFormSubmit);
        document.getElementById('clear-form-btn').addEventListener('click', clearWalletForm);

    } catch (error) {
        console.error('Failed to load wallets:', error);
        tableBody.innerHTML = '<tr><td colspan="3">Error loading wallets.</td></tr>';
    }
}

function handleWalletEdit(event) {
    const wallet = JSON.parse(event.target.dataset.wallet);
    document.getElementById('wallet-form-title').innerText = 'Edit Wallet';
    document.getElementById('wallet-original-name').value = wallet.name;
    document.getElementById('wallet-name').value = wallet.name;
    document.getElementById('wallet-address').value = wallet.value;
    window.scrollTo(0, document.body.scrollHeight);
}

function clearWalletForm() {
    document.getElementById('wallet-form-title').innerText = 'Add New Wallet';
    document.getElementById('wallet-form').reset();
    document.getElementById('wallet-original-name').value = '';
}

async function handleWalletFormSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const originalName = form.original_name.value;
    const walletData = { type: 'wallet', name: form.name.value, value: form.value.value };

    try {
        if (originalName) {
            await sheetDB.patch(`name/${originalName}?sheet=config`, walletData);
            alert('Wallet updated successfully!');
        } else {
            await sheetDB.post('?sheet=config', [walletData]);
            alert('Wallet created successfully!');
        }
        clearWalletForm();
        loadWalletsPage();
    } catch (error) {
        console.error('Failed to save wallet:', error);
        alert('Error saving wallet.');
    }
}

async function handleWalletDelete(event) {
    const name = event.target.dataset.name;
    if (confirm(`Are you sure you want to delete the wallet for "${name}"?`)) {
        try {
            await sheetDB.delete(`name/${name}?sheet=config`);
            alert('Wallet deleted successfully!');
            loadWalletsPage();
        } catch (error) {
            console.error('Failed to delete wallet:', error);
            alert('Error deleting wallet.');
        }
    }
}