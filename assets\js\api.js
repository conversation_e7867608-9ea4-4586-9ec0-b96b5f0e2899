// Generic function to create a SheetDB API handler from a config object
const createApiHandler = (dbConfig) => {
    const { apiUrl, bearerToken } = dbConfig;

    const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${bearerToken}`
    };

    return {
        get: async (params = '') => {
            try {
                const url = `${apiUrl}${params}`;
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });
                if (!response.ok) {
                    throw new Error(`Network response was not ok. Status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Fetch error for ${dbConfig.apiName}:`, error);
                return null;
            }
        },
        post: async (data) => {
            try {
                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify({ data: data })
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Post error for ${dbConfig.apiName}:`, error);
                return null;
            }
        },
        patch: async (condition, data) => {
            try {
                const url = `${apiUrl}/${condition}`;
                const response = await fetch(url, {
                    method: 'PATCH',
                    headers: headers,
                    body: JSON.stringify({ data: data })
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error(`Patch error for ${dbConfig.apiName}:`, error);
                return null;
            }
        }
    };
};

// Create API handlers using the configuration from config.js
// Note: config.js must be loaded before api.js in the HTML.
const configApi = createApiHandler(configDB);
const dataApi = createApiHandler(dataDB);