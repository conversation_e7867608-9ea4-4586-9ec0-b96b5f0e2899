document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    // Handle Registration
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value; // In a real app, hash this!

            // Check if user already exists
            const existingUsers = await dataApi.get(`?email=${email}`);
            if (existingUsers && existingUsers.length > 0) {
                alert('An account with this email already exists.');
                return;
            }

            const newUser = {
                id: 'auto',
                email: email,
                password: password, // Storing plain text is insecure. For demo purposes only.
                role: 'client',
                status: 'active',
                timestamp: new Date().toISOString()
            };

            const result = await dataApi.post(newUser);

            if (result && result.created) {
                alert('Registration successful! You can now log in.');
                window.location.href = 'login.html';
            } else {
                alert('Registration failed. Please try again.');
            }
        });
    }

    // Handle Login
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;

            const users = await dataApi.get(`?email=${email}`);
            const user = users ? users.find(u => u.password === password) : null; // Plain text password check

            if (user) {
                // Store session and redirect based on role
                localStorage.setItem('cryptoUser', JSON.stringify(user));
                if (user.role === 'admin') {
                    window.location.href = 'admin/index.html';
                } else {
                    window.location.href = 'dashboard/home.html';
                }
            } else {
                alert('Invalid email or password.');
            }
        });
    }
});

// Logout functionality
function logout() {
    localStorage.removeItem('cryptoUser');
    window.location.href = '/login.html';
}

// Protect routes
function protectRoute(allowedRoles) {
    const user = JSON.parse(localStorage.getItem('cryptoUser'));
    if (!user || !allowedRoles.includes(user.role)) {
        window.location.href = '/login.html';
    }
    return user;
}