// Initialize Supabase API if not already done
if (typeof supabaseAPI === 'undefined') {
    // Fallback to create supabaseAP<PERSON> if not loaded
    const supabaseAPI = new SupabaseAPI();
}

// Registration function
async function registerUser(e) {
    e.preventDefault();

    const name = document.getElementById('name')?.value || '';
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    if (!email || !password) {
        Swal.fire('Erreur', 'Veuillez remplir tous les champs requis.', 'error');
        return;
    }

    try {
        // Check if user already exists
        const existingUsers = await supabaseAPI.getUserByEmail(email);
        if (existingUsers && existingUsers.length > 0) {
            Swal.fire('Erreur', 'Un compte avec cette adresse email existe déjà.', 'error');
            return;
        }

        const newUser = {
            email: email,
            password: password, // In production, this should be hashed
            full_name: name,
            role: 'client',
            status: 'active',
            balance: 0.00,
            total_invested: 0.00,
            total_profit: 0.00,
            created_at: new Date().toISOString()
        };

        const result = await supabaseAPI.createUser(newUser);

        if (result && result.length > 0) {
            Swal.fire('Succès!', 'Inscription réussie! Vous pouvez maintenant vous connecter.', 'success');
            window.location.href = 'login.html';
        } else {
            throw new Error('Registration failed');
        }
    } catch (error) {
        console.error('Registration error:', error);
        Swal.fire('Erreur', 'L\'inscription a échoué. Veuillez réessayer.', 'error');
    }
}

// Login function
async function loginUser(e) {
    e.preventDefault();

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;

    if (!email || !password) {
        Swal.fire('Erreur', 'Veuillez remplir tous les champs.', 'error');
        return;
    }

    try {
        const users = await supabaseAPI.getUserByEmail(email);
        const user = users && users.length > 0 ? users.find(u => u.password === password) : null;

        if (user) {
            // Store session and redirect based on role
            localStorage.setItem('cryptoUser', JSON.stringify(user));
            localStorage.setItem('user', JSON.stringify(user)); // For compatibility with client.js

            if (user.role === 'admin') {
                window.location.href = 'admin.html';
            } else {
                window.location.href = 'dashboard/app.html';
            }
        } else {
            Swal.fire('Erreur', 'Email ou mot de passe invalide.', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        Swal.fire('Erreur', 'Erreur de connexion. Veuillez réessayer.', 'error');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    // Handle Registration
    if (registerForm) {
        registerForm.addEventListener('submit', registerUser);
    }

    // Handle Login
    if (loginForm) {
        loginForm.addEventListener('submit', loginUser);
    }
});

// Logout functionality
function logout() {
    localStorage.removeItem('cryptoUser');
    window.location.href = '/login.html';
}

// Protect routes
function protectRoute(allowedRoles) {
    const user = JSON.parse(localStorage.getItem('cryptoUser'));
    if (!user || !allowedRoles.includes(user.role)) {
        window.location.href = '/login.html';
    }
    return user;
}