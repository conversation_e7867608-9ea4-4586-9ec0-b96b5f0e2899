// Modern Client Dashboard JavaScript with Supabase Integration

document.addEventListener('DOMContentLoaded', function() {
    console.log('👤 Client Dashboard Loading...');
    initializeClientDashboard();
    loadClientData();
    initializeClientActions();
    initializeCharts();
    initializeRealTimeUpdates();
    console.log('✅ Client Dashboard Loaded Successfully');
});

// Initialize client dashboard functionality
function initializeClientDashboard() {
    console.log('📊 Initializing Client Dashboard...');

    // Initialize sidebar navigation
    initializeClientSidebar();

    // Initialize client-specific features
    initializeClientNotifications();
    initializeClientSearch();

    console.log('✅ Client Dashboard initialized');
}

// Initialize client sidebar
function initializeClientSidebar() {
    const sidebarLinks = document.querySelectorAll('.client-sidebar .nav-link');

    sidebarLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links
            sidebarLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            console.log(`🔗 Client navigation: ${this.textContent.trim()}`);
        });
    });
}

// Load client data with real Supabase integration
async function loadClientData() {
    console.log('📈 Loading Client Data...');

    try {
        // Wait for Supabase API to be ready
        if (!window.supabaseAPI || !window.supabaseAPI.isReady()) {
            console.log('⏳ Waiting for Supabase API...');
            await waitForSupabaseAPI();
        }

        // Get current user
        const currentUser = window.supabaseAPI.getCurrentUser();
        if (!currentUser) {
            console.log('❌ No authenticated user found');
            window.location.href = '../login-modern.html';
            return;
        }

        // Load real data from Supabase
        await loadPortfolioData(currentUser.id);
        await loadRecentTransactions(currentUser.id);
        await loadPerformanceData(currentUser.id);

        console.log('✅ Client data loaded successfully');
    } catch (error) {
        console.error('❌ Error loading client data:', error);
        showClientToast('Erreur lors du chargement des données', 'error');
    }
}

// Wait for Supabase API to be ready
async function waitForSupabaseAPI() {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max

    while (attempts < maxAttempts) {
        if (window.supabaseAPI && window.supabaseAPI.isReady()) {
            return true;
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    throw new Error('Supabase API not available');
}

// Load portfolio data from Supabase
async function loadPortfolioData(userId) {
    try {
        console.log('💰 Loading portfolio data...');

        // Get portfolio data from Supabase
        const portfolio = await window.supabaseAPI.getUserPortfolio(userId);

        // Update balance displays
        updateElement('totalBalance', formatCurrency(portfolio.totalBalance));
        updateElement('portfolioBalance', formatCurrency(portfolio.totalBalance));
        updateElement('dailyProfit', formatCurrency(portfolio.dailyProfit, true));

        // Update balance change indicator
        const balanceChange = document.querySelector('.balance-change');
        if (balanceChange) {
            balanceChange.className = `balance-change ${portfolio.dailyProfit >= 0 ? 'positive' : 'negative'}`;
            balanceChange.innerHTML = `
                <i class="fas fa-arrow-${portfolio.dailyProfit >= 0 ? 'up' : 'down'}"></i>
                ${portfolio.dailyChange.toFixed(1)}% (24h)
            `;
        }

        // Update asset values
        portfolio.assets.forEach((asset, index) => {
            const assetItems = document.querySelectorAll('.asset-item');
            if (assetItems[index]) {
                const valueElement = assetItems[index].querySelector('.asset-value');
                const changeElement = assetItems[index].querySelector('.asset-change');

                if (valueElement) valueElement.textContent = formatCurrency(asset.value);
                if (changeElement) {
                    changeElement.className = `asset-change ${asset.change >= 0 ? 'positive' : 'negative'}`;
                    changeElement.textContent = `${asset.change >= 0 ? '+' : ''}${asset.change}%`;
                }
            }
        });

        console.log('💰 Portfolio data updated');

    } catch (error) {
        console.error('❌ Error loading portfolio data:', error);
        // Fallback to simulated data
        await loadPortfolioDataFallback();
    }
}

// Load recent transactions from Supabase
async function loadRecentTransactions(userId) {
    try {
        console.log('📋 Loading recent transactions...');

        // Get transactions from Supabase
        const transactions = await window.supabaseAPI.getUserTransactions(userId, 10);

        const transactionsList = document.getElementById('recent-transactions-list');

        if (transactionsList && transactions.length > 0) {
            transactionsList.innerHTML = transactions.map(transaction => {
                const icon = getTransactionIcon(transaction.type);
                const timeAgo = getTimeAgo(transaction.created_at);

                return `
                    <div class="transaction-item">
                        <div class="transaction-icon ${transaction.type}">
                            <i class="fas ${icon}"></i>
                        </div>
                        <div class="transaction-details">
                            <div class="transaction-title">${getTransactionTitle(transaction)}</div>
                            <div class="transaction-time">${timeAgo}</div>
                        </div>
                        <div class="transaction-amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                            ${transaction.amount >= 0 ? '+' : ''}${formatCurrency(Math.abs(transaction.amount))}
                        </div>
                    </div>
                `;
            }).join('');

            console.log('📋 Recent transactions updated');
        } else {
            // Show empty state or fallback
            await loadRecentTransactionsFallback();
        }

    } catch (error) {
        console.error('❌ Error loading transactions:', error);
        // Fallback to simulated data
        await loadRecentTransactionsFallback();
    }
}

// Load performance data from Supabase
async function loadPerformanceData(userId) {
    try {
        console.log('📊 Loading performance data...');

        // Get portfolio data for performance calculation
        const portfolio = await window.supabaseAPI.getUserPortfolio(userId);

        // Update performance stats
        const perfStats = document.querySelectorAll('.perf-value');
        if (perfStats.length >= 2) {
            perfStats[0].textContent = `+${portfolio.profitPercentage.toFixed(1)}%`;
            perfStats[0].className = `perf-value ${portfolio.profitPercentage >= 0 ? 'positive' : 'negative'}`;

            perfStats[1].textContent = formatCurrency(portfolio.totalProfit, true);
            perfStats[1].className = `perf-value ${portfolio.totalProfit >= 0 ? 'positive' : 'negative'}`;
        }

        console.log('📊 Performance data updated');

    } catch (error) {
        console.error('❌ Error loading performance data:', error);
        // Fallback to simulated data
        await loadPerformanceDataFallback();
    }
}

// Utility functions for transactions
function getTransactionIcon(type) {
    switch (type) {
        case 'deposit': return 'fa-arrow-up';
        case 'withdrawal': return 'fa-arrow-down';
        case 'profit': return 'fa-coins';
        case 'investment': return 'fa-chart-line';
        case 'exchange': return 'fa-exchange-alt';
        default: return 'fa-circle';
    }
}

function getTransactionTitle(transaction) {
    switch (transaction.type) {
        case 'deposit': return 'Dépôt';
        case 'withdrawal': return 'Retrait';
        case 'profit': return 'Profit généré';
        case 'investment': return 'Investissement';
        case 'exchange': return 'Échange';
        default: return 'Transaction';
    }
}

function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return 'À l\'instant';
    if (diffInSeconds < 3600) return `Il y a ${Math.floor(diffInSeconds / 60)} min`;
    if (diffInSeconds < 86400) return `Il y a ${Math.floor(diffInSeconds / 3600)} h`;
    if (diffInSeconds < 604800) return `Il y a ${Math.floor(diffInSeconds / 86400)} j`;

    return date.toLocaleDateString('fr-FR');
}

// Fallback functions for when Supabase data is not available
async function loadPortfolioDataFallback() {
    console.log('🔄 Using fallback portfolio data...');

    const portfolio = {
        totalBalance: 45892.50,
        dailyProfit: 1234.56,
        dailyChange: 12.5,
        assets: [
            { name: 'Bitcoin', value: 20651, change: 2.4 },
            { name: 'Ethereum', value: 13768, change: -1.2 },
            { name: 'Cardano', value: 11473, change: 5.7 }
        ]
    };

    // Update UI with fallback data
    updateElement('totalBalance', formatCurrency(portfolio.totalBalance));
    updateElement('portfolioBalance', formatCurrency(portfolio.totalBalance));
    updateElement('dailyProfit', formatCurrency(portfolio.dailyProfit, true));

    const balanceChange = document.querySelector('.balance-change');
    if (balanceChange) {
        balanceChange.className = `balance-change positive`;
        balanceChange.innerHTML = `
            <i class="fas fa-arrow-up"></i>
            +${portfolio.dailyChange}% (24h)
        `;
    }
}

async function loadRecentTransactionsFallback() {
    console.log('🔄 Using fallback transaction data...');

    const transactions = [
        {
            type: 'deposit',
            title: 'Dépôt Bitcoin',
            time: 'Il y a 2 heures',
            amount: 1250.00,
            formattedAmount: '€1,250.00'
        },
        {
            type: 'profit',
            title: 'Profit généré',
            time: 'Hier',
            amount: 234.56,
            formattedAmount: '€234.56'
        },
        {
            type: 'investment',
            title: 'Nouvel investissement',
            time: 'Il y a 2 jours',
            amount: -500.00,
            formattedAmount: '€500.00'
        }
    ];

    const transactionsList = document.getElementById('recent-transactions-list');
    if (transactionsList) {
        transactionsList.innerHTML = transactions.map(transaction => `
            <div class="transaction-item">
                <div class="transaction-icon ${transaction.type}">
                    <i class="fas ${getTransactionIcon(transaction.type)}"></i>
                </div>
                <div class="transaction-details">
                    <div class="transaction-title">${transaction.title}</div>
                    <div class="transaction-time">${transaction.time}</div>
                </div>
                <div class="transaction-amount ${transaction.amount >= 0 ? 'positive' : 'negative'}">
                    ${transaction.amount >= 0 ? '+' : ''}${transaction.formattedAmount}
                </div>
            </div>
        `).join('');
    }
}

async function loadPerformanceDataFallback() {
    console.log('🔄 Using fallback performance data...');

    const performance = {
        return: 24.5,
        profitLoss: 8945.23
    };

    const perfStats = document.querySelectorAll('.perf-value');
    if (perfStats.length >= 2) {
        perfStats[0].textContent = `+${performance.return}%`;
        perfStats[0].className = 'perf-value positive';

        perfStats[1].textContent = formatCurrency(performance.profitLoss, true);
        perfStats[1].className = 'perf-value positive';
    }
}

// Initialize client actions
function initializeClientActions() {
    console.log('⚡ Initializing Client Actions...');

    // Quick action buttons
    const actionButtons = document.querySelectorAll('.action-btn');
    actionButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const action = this.dataset.action;
            handleClientAction(action);
        });
    });

    // Header action buttons
    const investBtn = document.getElementById('invest-btn');
    if (investBtn) {
        investBtn.addEventListener('click', () => handleClientAction('invest'));
    }

    const refreshBtn = document.getElementById('refresh-btn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
            showClientToast('Actualisation en cours...', 'info');
            loadClientData();
        });
    }

    // Chart period buttons
    const chartPeriods = document.querySelectorAll('.chart-period');
    chartPeriods.forEach(btn => {
        btn.addEventListener('click', function() {
            chartPeriods.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const period = this.dataset.period;
            console.log(`📊 Chart period changed: ${period}`);
            updatePerformanceChart(period);
        });
    });

    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    console.log('✅ Client actions initialized');
}

// Handle logout with Supabase
async function handleLogout() {
    try {
        console.log('🚪 Logging out...');

        if (window.supabaseAPI && window.supabaseAPI.isReady()) {
            await window.supabaseAPI.logout();
        }

        showClientToast('Déconnexion réussie', 'success');

        setTimeout(() => {
            window.location.href = '../login-modern.html';
        }, 1000);

    } catch (error) {
        console.error('❌ Logout error:', error);
        // Force redirect even if logout fails
        window.location.href = '../login-modern.html';
    }
}

// Handle client actions
function handleClientAction(action) {
    console.log(`🎯 Client action: ${action}`);

    switch (action) {
        case 'invest':
            showClientToast('Redirection vers les plans d\'investissement...', 'info');
            window.location.href = 'plans.html';
            break;
        case 'withdraw':
            showClientToast('Redirection vers les retraits...', 'info');
            // Implement withdrawal functionality
            break;
        case 'exchange':
            showClientToast('Redirection vers l\'exchange...', 'info');
            window.location.href = 'exchange.html';
            break;
        case 'history':
            showClientToast('Redirection vers l\'historique...', 'info');
            window.location.href = 'history.html';
            break;
        default:
            showClientToast(`Action ${action} en cours de développement`, 'warning');
    }
}

// Initialize charts
function initializeCharts() {
    console.log('📊 Initializing Charts...');

    // Initialize performance chart
    initializePerformanceChart();

    console.log('✅ Charts initialized');
}

// Initialize performance chart
function initializePerformanceChart() {
    const ctx = document.getElementById('performance-chart');
    if (!ctx) return;

    const chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
            datasets: [{
                label: 'Portfolio Value',
                data: [45000, 46200, 45800, 47100, 46900, 48200, 45892],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        color: '#6b7280'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(107, 114, 128, 0.1)'
                    },
                    ticks: {
                        color: '#6b7280',
                        callback: function(value) {
                            return '€' + value.toLocaleString();
                        }
                    }
                }
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#1d4ed8'
                }
            }
        }
    });

    // Store chart instance for updates
    window.performanceChart = chart;
}

// Update performance chart
function updatePerformanceChart(period) {
    if (!window.performanceChart) return;

    // Simulate different data for different periods
    const data = {
        '24h': [45000, 46200, 45800, 47100, 46900, 48200, 45892],
        '7d': [42000, 43500, 45000, 44200, 46800, 47500, 45892],
        '30d': [38000, 40000, 42000, 41000, 44000, 46000, 45892],
        '1y': [25000, 30000, 35000, 32000, 40000, 43000, 45892]
    };

    const labels = {
        '24h': ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
        '7d': ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
        '30d': ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4'],
        '1y': ['Jan', 'Mar', 'Mai', 'Jul', 'Sep', 'Nov', 'Déc']
    };

    window.performanceChart.data.labels = labels[period];
    window.performanceChart.data.datasets[0].data = data[period];
    window.performanceChart.update();

    console.log(`📊 Chart updated for period: ${period}`);
}

// Initialize real-time updates
function initializeRealTimeUpdates() {
    console.log('🔄 Initializing Real-time Updates...');

    // Update portfolio every 30 seconds
    setInterval(() => {
        const currentUser = window.supabaseAPI?.getCurrentUser();
        if (currentUser) {
            loadPortfolioData(currentUser.id);
        }
    }, 30000);

    // Update transactions every 60 seconds
    setInterval(() => {
        const currentUser = window.supabaseAPI?.getCurrentUser();
        if (currentUser) {
            loadRecentTransactions(currentUser.id);
        }
    }, 60000);

    console.log('✅ Real-time updates initialized');
}

// Initialize client notifications
function initializeClientNotifications() {
    const notificationBtn = document.getElementById('notification-btn');
    if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
            console.log('🔔 Client notifications clicked');
            showClientToast('Panel de notifications en cours de développement', 'info');
        });
    }
}

// Initialize client search
function initializeClientSearch() {
    const searchInput = document.getElementById('client-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            console.log(`🔍 Client search: ${query}`);
            // Implement search functionality
        });
    }
}

// Utility functions
function updateElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

function formatCurrency(amount, showSign = false) {
    const formatted = new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(Math.abs(amount));

    if (showSign && amount !== 0) {
        return (amount >= 0 ? '+' : '-') + formatted;
    }

    return formatted;
}

function showClientToast(message, type = 'info') {
    console.log(`🍞 Client Toast: ${message} (${type})`);

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `client-toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getToastColor(type)};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
        max-width: 400px;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

function getToastColor(type) {
    switch (type) {
        case 'success': return '#10b981';
        case 'error': return '#ef4444';
        case 'warning': return '#f59e0b';
        default: return '#3b82f6';
    }
}

// Export functions for external use
window.ClientDashboard = {
    loadClientData,
    showClientToast,
    handleClientAction
};

console.log('👤 Client Dashboard JavaScript with Supabase Integration Loaded Successfully');