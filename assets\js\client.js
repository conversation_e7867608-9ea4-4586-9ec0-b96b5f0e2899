// Function for the public landing page
async function loadPublicPlans() {
    const container = document.getElementById('plans-container');
    if (!container) return;

    container.innerHTML = '<div class="loader">Chargement des plans...</div>'; // Show loader

    try {
        const plans = await supabaseAPI.getInvestmentPlans();
        if (!plans || plans.length === 0) {
            throw new Error('No investment plans found.');
        }

        let plansHTML = '';
        plans.forEach((plan, index) => {
            const features = plan.description ? plan.description.split(',').map(item => `<li>${item.trim()}</li>`).join('') : '<li>Plan d\'investissement</li>';
            plansHTML += `
                <div class="plan-card" data-aos="fade-up" data-aos-delay="${index * 100}">
                    <div class="plan-header">
                        <h3>${plan.name}</h3>
                        <p class="price">${plan.investment}</p>
                        <p class="roi">${plan.roi_percentage}% ROI</p>
                    </div>
                    <ul class="plan-features">
                        ${features}
                        <li>Durée: ${plan.duration_days} jours</li>
                        <li>Montant: €${plan.min_amount} - €${plan.max_amount}</li>
                    </ul>
                    <a href="register.html" class="btn btn-primary">Choisir ce plan</a>
                </div>
            `;
        });

        container.innerHTML = plansHTML;
    } catch (error) {
        console.error('Error loading plans:', error);
        container.innerHTML = '<p class="error-message">Impossible de charger les plans d\'investissement pour le moment.</p>';
    }
}

// --- SPA ROUTER AND LAYOUT --- //

const routes = {
    'home.html': { loader: loadHomePage, title: 'Tableau de Bord' },
    'wallet.html': { loader: loadWalletPage, title: 'Mon Portefeuille' },
    'plans.html': { loader: loadPlansPage, title: 'Plans d\'Investissement' },
    'history.html': { loader: loadHistoryPage, title: 'Historique' },
    'profile.html': { loader: loadProfilePage, title: 'Mon Profil' },
    'exchange.html': { loader: loadExchangePage, title: 'Exchange' },
};

async function navigateTo(page, user) {
    const pageContent = document.getElementById('page-content');
    if (!pageContent) return;

    pageContent.innerHTML = '<div class="loader"></div>';

    try {
        const response = await fetch(page);
        if (!response.ok) throw new Error(`Page not found: ${page}`);
        const content = await response.text();
        pageContent.innerHTML = content;

        // Update active link in sidebar
        document.querySelectorAll('#app-nav a').forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('onclick').includes(`'${page}'`)) {
                link.classList.add('active');
            }
        });

        // Call the specific loader for the page
        const route = routes[page];
        if (route) {
            if (typeof route.loader === 'function') {
                route.loader(user);
            }
            if (route.title) {
                document.querySelector('#app-header h1').textContent = route.title;
            }
        }
        lucide.createIcons(); // Re-run lucide to render new icons

    } catch (error) {
        console.error('Failed to load page:', error);
        pageContent.innerHTML = `<div class="content-card"><p>Erreur de chargement de la page: ${error.message}</p></div>`;
    }
}

function renderAppLayout(user) {
    const sidebar = document.getElementById('sidebar');
    const header = document.getElementById('app-header');

    if (!sidebar || !header) return;

    // Render Sidebar
    sidebar.innerHTML = `
        <a href="../index.html" class="logo">CryptoBoost</a>
        <nav id="app-nav">
            <ul>
                <li><a href="#" class="active" onclick="event.preventDefault(); navigateTo('home.html', JSON.parse(localStorage.getItem('user')))"><i data-lucide="layout-grid"></i> Tableau de bord</a></li>
                <li><a href="#" onclick="event.preventDefault(); navigateTo('wallet.html', JSON.parse(localStorage.getItem('user')))"><i data-lucide="wallet"></i> Mon Portefeuille</a></li>
                <li><a href="#" onclick="event.preventDefault(); navigateTo('plans.html', JSON.parse(localStorage.getItem('user')))"><i data-lucide="bar-chart-2"></i> Plans d'Investissement</a></li>
                <li><a href="#" onclick="event.preventDefault(); navigateTo('history.html', JSON.parse(localStorage.getItem('user')))"><i data-lucide="history"></i> Historique</a></li>
                <li><a href="#" onclick="event.preventDefault(); navigateTo('exchange.html', JSON.parse(localStorage.getItem('user')))"><i data-lucide="repeat"></i> Exchange</a></li>
            </ul>
        </nav>
        <div class="sidebar-footer">
            <a href="#" onclick="event.preventDefault(); navigateTo('profile.html', JSON.parse(localStorage.getItem('user')))" class="nav-link"><i data-lucide="user"></i> <span>Mon Profil</span></a>
            <a href="#" id="logout-btn" class="nav-link"><i data-lucide="log-out"></i> <span>Déconnexion</span></a>
        </div>
    `;

    // Render Header
    header.innerHTML = `
        <h1>Tableau de Bord</h1>
        <div class="user-menu">
            <div class="welcome-text">
                <span>Bienvenue,</span>
                <strong id="user-name">${user.name}</strong>
            </div>
            <img src="https://i.pravatar.cc/40?u=${user.email}" alt="User Avatar">
        </div>
    `;

    // Attach logout event listener
    document.getElementById('logout-btn').addEventListener('click', logoutUser);

    lucide.createIcons();
}

function logoutUser() {
    localStorage.removeItem('user');
    window.location.href = 'login.html';
}

function loadDashboard(user) {
    renderAppLayout(user);
    navigateTo('home.html', user);
}

document.addEventListener('DOMContentLoaded', () => {
    const path = window.location.pathname;

    if (path.includes('/dashboard/app.html')) {
        const user = protectRoute(['client']);
        if (!user) return;
        loadDashboard(user);
    } else if (path === '/' || path.endsWith('/index.html')) {
        loadPublicPlans();
        // Handle FAQ Accordion
        const faqItems = document.querySelectorAll('.faq-item');
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            question.addEventListener('click', () => {
                item.classList.toggle('active');
            });
        });

        // Mobile menu toggle
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const mainNav = document.getElementById('main-nav');
        if(menuToggle && mainNav) {
            menuToggle.addEventListener('click', () => {
                mainNav.classList.toggle('active');
                const icon = menuToggle.querySelector('i');
                if (mainNav.classList.contains('active')) {
                    icon.setAttribute('data-lucide', 'x');
                } else {
                    icon.setAttribute('data-lucide', 'menu');
                }
                lucide.createIcons();
            });
        }
    }
});


// --- DASHBOARD FUNCTIONS --- //

async function loadHomePage(user) {
    try {
        // Get user transactions
        const userTransactions = await supabaseAPI.getUserTransactions(user.email);
        const investments = userTransactions ? userTransactions.filter(t => t.type === 'investment') : [];

        const totalInvested = investments.reduce((sum, inv) => sum + parseFloat(inv.amount || 0), 0);
        const totalProfit = user.total_profit || 0;
        const activeBots = investments.filter(inv => inv.status === 'active').length;

        // Update dashboard elements
        const totalInvestedEl = document.getElementById('total-invested');
        const totalProfitEl = document.getElementById('total-profit');
        const activeBotsEl = document.getElementById('active-bots');

        if (totalInvestedEl) totalInvestedEl.textContent = `€${totalInvested.toFixed(2)}`;
        if (totalProfitEl) totalProfitEl.textContent = `€${totalProfit.toFixed(2)}`;
        if (activeBotsEl) activeBotsEl.textContent = activeBots;

        const recentActivityBody = document.getElementById('recent-activity-body');
        if (recentActivityBody) {
            recentActivityBody.innerHTML = investments.slice(0, 5).map(inv => `
                <tr>
                    <td>${new Date(inv.created_at).toLocaleDateString()}</td>
                    <td>${inv.plan_name || 'N/A'}</td>
                    <td>€${parseFloat(inv.amount).toFixed(2)}</td>
                    <td><span class="status status-${inv.status}">${inv.status}</span></td>
                </tr>
            `).join('') || '<tr><td colspan="4">Aucune activité récente.</td></tr>';
        }

    } catch (error) {
        console.error('Error loading home page data:', error);
    }
}

async function loadWalletPage(user) {
    try {
        const userTransactions = await supabaseAPI.getUserTransactions(user.email);
        const walletConfigs = await supabaseAPI.getCompanyWallets();
        
        // Calculate balances from transactions
        const balances = {};
        walletConfigs.forEach(conf => balances[conf.crypto] = 0);
        userTransactions.forEach(tx => {
            if (tx.action === 'deposit' && tx.status_action === 'completed') {
                balances[tx.crypto] = (balances[tx.crypto] || 0) + parseFloat(tx.amount);
            } else if (tx.action === 'withdraw' && tx.status_action === 'completed') {
                balances[tx.crypto] = (balances[tx.crypto] || 0) - parseFloat(tx.amount);
            }
        });

        const walletContainer = document.getElementById('wallet-container');
        walletContainer.innerHTML = Object.entries(balances).map(([currency, balance]) => `
            <div class="wallet-card">
                <div class="wallet-icon">${currency}</div>
                <div class="wallet-info">
                    <span class="wallet-balance">${balance.toFixed(8)}</span>
                    <span class="wallet-currency">${currency}</span>
                </div>
            </div>
        `).join('');

        // Populate dropdowns
        const depositCurrencySelect = document.getElementById('deposit-currency');
        const withdrawCurrencySelect = document.getElementById('withdraw-currency');
        const currencyOptions = walletConfigs.map(c => `<option value="${c.crypto}">${c.name}</option>`).join('');
        depositCurrencySelect.innerHTML = '<option value="">Choisir...</option>' + currencyOptions;
        withdrawCurrencySelect.innerHTML = '<option value="">Choisir...</option>' + currencyOptions;

        // Deposit logic
        depositCurrencySelect.addEventListener('change', (e) => {
            const currency = e.target.value;
            const depositInfo = document.getElementById('deposit-info');
            if (!currency) {
                depositInfo.classList.add('hidden');
                return;
            }
            const walletConfig = walletConfigs.find(c => c.crypto === currency);
            document.getElementById('deposit-address').textContent = walletConfig.company_wallet;
            const qrCodeContainer = document.getElementById('deposit-qr-code');
            qrCodeContainer.innerHTML = '';
            new QRCode(qrCodeContainer, { text: walletConfig.company_wallet, width: 128, height: 128 });
            depositInfo.classList.remove('hidden');
        });

        // Withdraw logic
        document.getElementById('withdraw-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const currency = document.getElementById('withdraw-currency').value;
            const amount = parseFloat(document.getElementById('withdraw-amount').value);
            const address = document.getElementById('withdraw-address').value;

            if (!currency || !amount || !address) {
                return Swal.fire('Erreur', 'Veuillez remplir tous les champs.', 'error');
            }

            if (amount <= 0) {
                return Swal.fire('Erreur', 'Le montant doit être positif.', 'error');
            }

            // Balance validation
            if (balances[currency] < amount) {
                return Swal.fire('Erreur', 'Solde insuffisant pour ce retrait.', 'error');
            }

            const withdrawData = {
                email: user.email,
                action: 'withdraw',
                crypto: currency,
                amount: amount,
                address: address,
                status_action: 'pending', // Withdrawals usually require approval
                timestamp: new Date().toISOString(),
            };

            try {
                const postResult = await supabaseAPI.createTransaction(withdrawData);
                if (postResult && postResult.length > 0) {
                    Swal.fire('Demande Soumise', 'Votre demande de retrait est en cours de traitement.', 'success');
                    e.target.reset();
                    navigateTo('wallet'); // Refresh wallet to show updated balance after withdrawal is processed (simulation)
                } else {
                    throw new Error('API post failed for withdrawal');
                }
            } catch (error) {
                console.error('Error during withdrawal:', error);
                Swal.fire('Erreur', 'La demande de retrait a échoué.', 'error');
            }
        });

    } catch (error) {
        console.error('Error loading wallet:', error);
    }
}

async function loadPlansPage() {
    const plansContainer = document.getElementById('plans-container');
    if (!plansContainer) return;
    plansContainer.innerHTML = '<div class="loader">Chargement des plans...</div>';

    try {
        const plans = await supabaseAPI.getInvestmentPlans();
        if (plans && plans.length > 0) {
            plansContainer.innerHTML = '<div class="plans-grid">' + plans.map(plan => `
                <div class="plan-card">
                    <h3>${plan.name}</h3>
                    <p class="plan-investment">${plan.investment} Investissement</p>
                    <p class="plan-roi">${plan.roi_percentage}% ROI sur ${plan.duration_days} jours</p>
                    <p class="plan-range">€${plan.min_amount} - €${plan.max_amount}</p>
                    <div class="plan-description">${plan.description || ''}</div>
                    <button class="btn btn-primary" onclick="invest('${plan.name}')">Investir Maintenant</button>
                </div>
            `).join('') + '</div>';
        } else {
            plansContainer.innerHTML = '<p class="info">Aucun plan d\'investissement disponible pour le moment.</p>';
        }
    } catch (error) {
        console.error('Error loading plans:', error);
        plansContainer.innerHTML = '<p class="error">Impossible de charger les plans d\'investissement.</p>';
    }
}

async function loadHistoryPage(user) {
    const historyBody = document.getElementById('history-table-body');
    if (!historyBody) return;
    historyBody.innerHTML = '<tr><td colspan="5"><div class="loader"></div></td></tr>';

    try {
        const userTransactions = await supabaseAPI.getUserTransactions(user.email);
        const transactions = userTransactions.filter(t => ['deposit', 'withdrawal', 'investment'].includes(t.type));
        
        let historyHTML = transactions.map(tx => `
            <tr>
                <td>${new Date(tx.created_at).toLocaleString()}</td>
                <td>${tx.type || 'N/A'}</td>
                <td>${tx.user_notes || tx.plan_name || 'N/A'}</td>
                <td>${tx.amount || 'N/A'} ${tx.currency || 'EUR'}</td>
                <td><span class="status status-${tx.status}">${tx.status}</span></td>
            </tr>
        `).join('');
        historyBody.innerHTML = historyHTML || '<tr><td colspan="5">Aucun historique trouvé.</td></tr>';
    } catch (error) {
        console.error('Error loading history:', error);
        historyBody.innerHTML = '<tr><td colspan="5" class="error">Erreur de chargement de l\'historique.</td></tr>';
    }
}

async function loadProfilePage(user) {
    document.getElementById('profile-name').value = user.name || '';
    document.getElementById('profile-email').value = user.email || '';

    document.getElementById('profile-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        const newName = document.getElementById('profile-name').value;
        const newPassword = document.getElementById('profile-password').value;

        try {
            const dataToUpdate = { name: newName };
            if (newPassword) {
                dataToUpdate.password = newPassword; // Hashing should be done server-side
            }

            await supabaseAPI.updateUser(user.email, dataToUpdate);
            Swal.fire('Succès', 'Profil mis à jour.', 'success');
            user.name = newName;
            localStorage.setItem('user', JSON.stringify(user));
            renderAppLayout(user);
        } catch (error) {
            console.error('Error updating profile:', error);
            Swal.fire('Erreur', 'La mise à jour du profil a échoué.', 'error');
        }
    });
}

async function loadExchangePage(user) {
    const fromCurrency = document.getElementById('from-currency');
    const toCurrency = document.getElementById('to-currency');
    if(!fromCurrency || !toCurrency) return;
    
    try {
        const walletConfigs = await supabaseAPI.getCompanyWallets();
        const optionsHTML = walletConfigs.map(c => `<option value="${c.crypto}">${c.name}</option>`).join('');
        fromCurrency.innerHTML = optionsHTML;
        toCurrency.innerHTML = optionsHTML;
    } catch (error) {
        console.error('Error loading currencies for exchange:', error);
    }


    // Add event listeners for exchange calculation
    const fromAmountInput = document.getElementById('from-amount');
    const fromCurrencySelect = document.getElementById('from-currency');
    const toCurrencySelect = document.getElementById('to-currency');

    [fromAmountInput, fromCurrencySelect, toCurrencySelect].forEach(el => {
        el.addEventListener('input', () => updateExchangeRate(user));
    });

    // Add event listener for form submission
    document.getElementById('exchange-form').addEventListener('submit', async (e) => {
        e.preventDefault();
        const fromAmount = parseFloat(fromAmountInput.value);
        const fromCurrency = fromCurrencySelect.value;
        const toCurrency = toCurrencySelect.value;
        const toAmount = parseFloat(document.getElementById('to-amount').value);

        if (!fromAmount || fromAmount <= 0 || !fromCurrency || !toCurrency) {
            return Swal.fire('Erreur', 'Veuillez remplir tous les champs pour l\'échange.', 'error');
        }

        if (fromCurrency === toCurrency) {
            return Swal.fire('Erreur', 'Les devises doivent être différentes.', 'error');
        }

        // This is a simplified balance check. In a real app, this would be more robust.
        const userTransactions = await supabaseAPI.getUserTransactions(user.email);
        const balances = {};
        userTransactions.forEach(tx => {
             if (tx.action === 'deposit' && tx.status_action === 'completed') {
                balances[tx.crypto] = (balances[tx.crypto] || 0) + parseFloat(tx.amount);
            } else if (tx.action === 'withdraw' && tx.status_action === 'completed') {
                balances[tx.crypto] = (balances[tx.crypto] || 0) - parseFloat(tx.amount);
            }
        });

        if (!balances[fromCurrency] || balances[fromCurrency] < fromAmount) {
            return Swal.fire('Erreur', 'Solde insuffisant pour effectuer cet échange.', 'error');
        }

        const exchangeData = {
            email: user.email,
            action: 'exchange',
            details: `Exchange ${fromAmount} ${fromCurrency} to ${toAmount} ${toCurrency}`,
            amount: fromAmount, // Log the 'from' amount
            crypto: fromCurrency, // Log the 'from' currency
            status_action: 'completed',
            timestamp: new Date().toISOString(),
        };

        try {
            const postResult = await supabaseAPI.createTransaction(exchangeData);
            if (postResult && postResult.length > 0) {
                Swal.fire('Succès', 'Échange effectué avec succès.', 'success');
                navigateTo('wallet'); // Go to wallet to see updated balances
            } else {
                throw new Error('API post failed for exchange');
            }
        } catch (error) {
            console.error('Error during exchange:', error);
            Swal.fire('Erreur', 'L\'échange a échoué.', 'error');
        }
    });
}

async function invest(planName) {
    try {
        const user = JSON.parse(localStorage.getItem('user')) || JSON.parse(localStorage.getItem('cryptoUser'));
        if (!user) return Swal.fire('Erreur', 'Utilisateur non connecté.', 'error');

        const plans = await supabaseAPI.getInvestmentPlans();
        const plan = plans ? plans.find(p => p.name === planName) : null;
        if (!plan) {
            return Swal.fire('Erreur', 'Plan d\'investissement non trouvé.', 'error');
        }

        const result = await Swal.fire({
            title: `Confirmer l'investissement`,
            html: `Vous êtes sur le point d'investir dans le plan <b>${plan.name}</b>.<br>
                   Montant: <b>€${plan.min_amount} - €${plan.max_amount}</b><br>
                   ROI: <b>${plan.roi_percentage}%</b> sur <b>${plan.duration_days} jours</b>`,
            icon: 'info',
            showCancelButton: true,
            confirmButtonText: 'Confirmer',
            cancelButtonText: 'Annuler'
        });

        if (result.isConfirmed) {
            const investmentData = {
                user_email: user.email,
                type: 'investment',
                amount: plan.min_amount, // Default to minimum amount
                status: 'active',
                plan_name: plan.name,
                notes: `Investment in ${plan.name} plan`
            };

            const postResult = await supabaseAPI.createTransaction(investmentData);
            if (postResult && postResult.length > 0) {
                Swal.fire('Succès!', 'Votre investissement a été activé.', 'success');
                navigateTo('home.html', user); // Refresh home page to see new stats
            } else {
                throw new Error('Transaction creation failed');
            }
        }
    } catch (error) {
        console.error('Error during investment:', error);
        Swal.fire('Erreur', 'Une erreur est survenue lors de l\'investissement.', 'error');
    }
}

function updateExchangeRate() {
    // This is a placeholder for a real-time rate fetch
    const rate = 30.5; // Example: 1 BTC = 30.5 ETH
    const fromAmount = parseFloat(document.getElementById('from-amount').value) || 0;
    const toAmount = fromAmount * rate;
    document.getElementById('to-amount').value = toAmount.toFixed(8);
    
    const fromCurrency = document.getElementById('from-currency').value;
    const toCurrency = document.getElementById('to-currency').value;
    document.getElementById('exchange-rate').textContent = `Taux: 1 ${fromCurrency} ≈ ${rate} ${toCurrency}`;
}