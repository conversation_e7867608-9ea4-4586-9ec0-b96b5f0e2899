// CryptoBoost Futuristic Effects

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Futuristic Effects...');
    
    initializeMatrixRain();
    initializeParticles();
    initializeCounters();
    initializeScrollEffects();
    initializeHoverEffects();
    initializeCyberEffects();
    
    console.log('✅ Futuristic Effects Loaded');
});

// Matrix Rain Effect
function initializeMatrixRain() {
    const matrixContainer = document.getElementById('matrix-rain');
    if (!matrixContainer) return;
    
    const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const columns = Math.floor(window.innerWidth / 20);
    
    function createMatrixChar() {
        const char = document.createElement('div');
        char.className = 'matrix-char';
        char.textContent = characters[Math.floor(Math.random() * characters.length)];
        char.style.left = Math.random() * 100 + '%';
        char.style.animationDuration = (Math.random() * 3 + 2) + 's';
        char.style.opacity = Math.random() * 0.8 + 0.2;
        
        matrixContainer.appendChild(char);
        
        setTimeout(() => {
            if (char.parentNode) {
                char.parentNode.removeChild(char);
            }
        }, 5000);
    }
    
    // Create initial matrix characters
    for (let i = 0; i < columns / 3; i++) {
        setTimeout(() => createMatrixChar(), Math.random() * 2000);
    }
    
    // Continue creating characters
    setInterval(() => {
        if (Math.random() < 0.3) {
            createMatrixChar();
        }
    }, 200);
}

// Floating Particles Effect
function initializeParticles() {
    const particleContainers = document.querySelectorAll('.hero-particles, .auth-particles');
    
    particleContainers.forEach(container => {
        if (!container) return;
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 8 + 5) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';
            
            // Random colors
            const colors = ['var(--cyber-blue)', 'var(--cyber-purple)', 'var(--cyber-pink)', 'var(--cyber-green)'];
            particle.style.background = colors[Math.floor(Math.random() * colors.length)];
            particle.style.boxShadow = `0 0 10px ${colors[Math.floor(Math.random() * colors.length)]}`;
            
            container.appendChild(particle);
            
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 10000);
        }
        
        // Create initial particles
        for (let i = 0; i < 20; i++) {
            setTimeout(() => createParticle(), Math.random() * 3000);
        }
        
        // Continue creating particles
        setInterval(() => {
            if (Math.random() < 0.4) {
                createParticle();
            }
        }, 500);
    });
}

// Animated Counters
function initializeCounters() {
    const counters = document.querySelectorAll('[data-count]');
    
    const animateCounter = (element) => {
        const target = parseInt(element.dataset.count);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            if (target > 1000000) {
                element.textContent = (current / 1000000).toFixed(1) + 'M';
            } else if (target > 1000) {
                element.textContent = (current / 1000).toFixed(0) + 'K';
            } else {
                element.textContent = Math.floor(current).toLocaleString();
            }
        }, 16);
    };
    
    // Intersection Observer for counters
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => observer.observe(counter));
}

// Scroll Effects
function initializeScrollEffects() {
    const nav = document.getElementById('cyber-nav');
    if (!nav) return;
    
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
        const currentScrollY = window.scrollY;
        
        if (currentScrollY > 100) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
        
        // Parallax effect for hero elements
        const heroElements = document.querySelectorAll('.floating-crypto');
        heroElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            element.style.transform = `translateY(${currentScrollY * speed}px) rotate(${currentScrollY * 0.1}deg)`;
        });
        
        lastScrollY = currentScrollY;
    });
}

// Hover Effects
function initializeHoverEffects() {
    // Card hover effects
    const cards = document.querySelectorAll('.card, .demo-account, .feature-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3), 0 0 30px rgba(0, 212, 255, 0.2)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
    
    // Button particle effects
    const buttons = document.querySelectorAll('.btn-primary');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            createButtonParticles(this);
        });
    });
}

// Create button particle effect
function createButtonParticles(button) {
    const particleContainer = button.querySelector('.btn-particles');
    if (!particleContainer) return;
    
    for (let i = 0; i < 10; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: var(--cyber-blue);
                border-radius: 50%;
                pointer-events: none;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: particleExplode 1s ease-out forwards;
            `;
            
            particleContainer.appendChild(particle);
            
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1000);
        }, i * 50);
    }
}

// Cyber Effects
function initializeCyberEffects() {
    // Glitch effect for titles
    const glitchElements = document.querySelectorAll('.hero-title, .auth-title');
    
    glitchElements.forEach(element => {
        setInterval(() => {
            if (Math.random() < 0.1) {
                element.style.textShadow = `
                    2px 0 var(--cyber-red),
                    -2px 0 var(--cyber-blue),
                    0 0 10px var(--cyber-purple)
                `;
                
                setTimeout(() => {
                    element.style.textShadow = '';
                }, 100);
            }
        }, 2000);
    });
    
    // Data flow effect
    const dataFlowElements = document.querySelectorAll('.trading-interface, .auth-container');
    
    dataFlowElements.forEach(element => {
        setInterval(() => {
            const line = document.createElement('div');
            line.style.cssText = `
                position: absolute;
                top: ${Math.random() * 100}%;
                left: -100%;
                width: 100%;
                height: 1px;
                background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
                animation: dataFlow 2s ease-out forwards;
                pointer-events: none;
                z-index: 1;
            `;
            
            element.appendChild(line);
            
            setTimeout(() => {
                if (line.parentNode) {
                    line.parentNode.removeChild(line);
                }
            }, 2000);
        }, 3000 + Math.random() * 2000);
    });
}

// Boot Sequence Animation
function showBootSequence() {
    const bootSequence = document.getElementById('boot-sequence');
    const progressBar = document.getElementById('boot-progress-bar');
    
    if (!bootSequence || !progressBar) return;
    
    bootSequence.classList.add('active');
    
    // Animate progress bar
    setTimeout(() => {
        progressBar.style.width = '100%';
    }, 500);
    
    // Hide boot sequence after animation
    setTimeout(() => {
        bootSequence.classList.remove('active');
    }, 4000);
}

// Live Stats Animation
function animateLiveStats() {
    const onlineTraders = document.getElementById('online-traders');
    const dailyVolume = document.getElementById('daily-volume');
    
    if (onlineTraders) {
        setInterval(() => {
            const current = parseInt(onlineTraders.textContent.replace(/,/g, ''));
            const change = Math.floor(Math.random() * 20) - 10;
            const newValue = Math.max(40000, current + change);
            onlineTraders.textContent = newValue.toLocaleString();
        }, 5000);
    }
    
    if (dailyVolume) {
        setInterval(() => {
            const values = ['€2.3M', '€2.4M', '€2.5M', '€2.6M', '€2.7M'];
            dailyVolume.textContent = values[Math.floor(Math.random() * values.length)];
        }, 8000);
    }
}

// Crypto Ticker Animation
function animateCryptoTicker() {
    const tickerItems = document.querySelectorAll('.ticker-item');
    
    tickerItems.forEach(item => {
        const priceElement = item.querySelector('.crypto-price');
        const changeElement = item.querySelector('.crypto-change');
        
        if (!priceElement || !changeElement) return;
        
        setInterval(() => {
            // Simulate price changes
            const currentPrice = parseFloat(priceElement.textContent.replace('€', '').replace(',', ''));
            const changePercent = (Math.random() - 0.5) * 10; // -5% to +5%
            const newPrice = currentPrice * (1 + changePercent / 100);
            
            priceElement.textContent = '€' + newPrice.toLocaleString('fr-FR', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            });
            
            changeElement.textContent = (changePercent >= 0 ? '+' : '') + changePercent.toFixed(1) + '%';
            changeElement.className = 'crypto-change ' + (changePercent >= 0 ? 'positive' : 'negative');
            
            // Flash effect
            item.style.background = changePercent >= 0 ? 
                'rgba(16, 185, 129, 0.1)' : 
                'rgba(239, 68, 68, 0.1)';
            
            setTimeout(() => {
                item.style.background = '';
            }, 500);
            
        }, 3000 + Math.random() * 2000);
    });
}

// Initialize all animations when page loads
window.addEventListener('load', () => {
    setTimeout(() => {
        animateLiveStats();
        animateCryptoTicker();
    }, 1000);
});

// CSS Animations (added via JavaScript)
const style = document.createElement('style');
style.textContent = `
    @keyframes particleExplode {
        0% {
            transform: scale(0) translate(0, 0);
            opacity: 1;
        }
        100% {
            transform: scale(1) translate(${Math.random() * 100 - 50}px, ${Math.random() * 100 - 50}px);
            opacity: 0;
        }
    }
    
    @keyframes dataFlow {
        0% {
            left: -100%;
            opacity: 0;
        }
        50% {
            opacity: 1;
        }
        100% {
            left: 100%;
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Export functions for external use
window.FuturisticEffects = {
    showBootSequence,
    createButtonParticles,
    animateLiveStats,
    animateCryptoTicker
};

console.log('🌟 Futuristic Effects System Loaded Successfully');
