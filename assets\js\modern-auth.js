// Modern Authentication JavaScript with Supabase Integration

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔐 Modern Authentication Loading...');
    initializeAuth();
    initializePasswordToggle();
    initializeDemoAccounts();
    initializeFormValidation();
    initializeAnimations();
    console.log('✅ Modern Authentication Loaded Successfully');
});

// Initialize authentication functionality
function initializeAuth() {
    console.log('🔑 Initializing Authentication...');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
        console.log('✅ Login form initialized');
    }

    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
        console.log('✅ Register form initialized');
    }

    // Google login
    const googleLoginBtn = document.getElementById('google-login');
    if (googleLoginBtn) {
        googleLoginBtn.addEventListener('click', handleGoogleLogin);
        console.log('✅ Google login initialized');
    }
}

// Handle login form submission with real Supabase authentication
async function handleLogin(e) {
    e.preventDefault();
    console.log('🔐 Login form submitted');

    const submitBtn = document.getElementById('login-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnSpinner = submitBtn.querySelector('.btn-spinner');

    // Show loading state
    setLoadingState(submitBtn, btnText, btnSpinner, true);

    try {
        const formData = new FormData(e.target);
        const email = formData.get('email');
        const password = formData.get('password');
        const remember = formData.get('remember');

        console.log(`📧 Login attempt: ${email}`);

        // Clear previous errors
        clearFormErrors();

        // Validate inputs
        if (!validateEmail(email)) {
            showFieldError('email', 'Veuillez entrer une adresse email valide');
            return;
        }

        if (!validatePassword(password)) {
            showFieldError('password', 'Le mot de passe doit contenir au moins 6 caractères');
            return;
        }

        // Wait for Supabase API to be ready
        if (!window.supabaseAPI || !window.supabaseAPI.isReady()) {
            console.log('⏳ Waiting for Supabase API...');
            await waitForSupabaseAPI();
        }

        // Real Supabase authentication
        const result = await window.supabaseAPI.login(email, password);

        if (result.success) {
            // Success - redirect to dashboard
            showSuccessMessage('Connexion réussie ! Redirection...');

            // Store remember me preference
            if (remember) {
                localStorage.setItem('rememberMe', 'true');
            }

            setTimeout(async () => {
                // Check if user is admin
                const isAdmin = await window.supabaseAPI.isAdmin(result.user.id);

                if (isAdmin) {
                    console.log('🔑 Admin login - redirecting to admin dashboard');
                    window.location.href = 'admin/dashboard.html';
                } else {
                    console.log('👤 User login - redirecting to client dashboard');
                    window.location.href = 'dashboard/home-modern.html';
                }
            }, 1500);
        }

    } catch (error) {
        console.error('❌ Login error:', error);
        showErrorMessage(error.message || 'Erreur de connexion. Veuillez réessayer.');
    } finally {
        setLoadingState(submitBtn, btnText, btnSpinner, false);
    }
}

// Handle register form submission with real Supabase
async function handleRegister(e) {
    e.preventDefault();
    console.log('📝 Register form submitted');

    const submitBtn = document.getElementById('register-btn');
    const btnText = submitBtn.querySelector('.btn-text');
    const btnSpinner = submitBtn.querySelector('.btn-spinner');

    // Show loading state
    setLoadingState(submitBtn, btnText, btnSpinner, true);

    try {
        const formData = new FormData(e.target);
        const firstName = formData.get('firstName');
        const lastName = formData.get('lastName');
        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const terms = formData.get('terms');

        console.log(`📧 Registration attempt: ${email}`);

        // Clear previous errors
        clearFormErrors();

        // Validate inputs
        if (!firstName || firstName.length < 2) {
            showFieldError('firstName', 'Le prénom doit contenir au moins 2 caractères');
            return;
        }

        if (!lastName || lastName.length < 2) {
            showFieldError('lastName', 'Le nom doit contenir au moins 2 caractères');
            return;
        }

        if (!validateEmail(email)) {
            showFieldError('email', 'Veuillez entrer une adresse email valide');
            return;
        }

        if (!validatePassword(password)) {
            showFieldError('password', 'Le mot de passe doit contenir au moins 8 caractères avec majuscules, minuscules et chiffres');
            return;
        }

        if (password !== confirmPassword) {
            showFieldError('confirmPassword', 'Les mots de passe ne correspondent pas');
            return;
        }

        if (!terms) {
            showFieldError('terms', 'Vous devez accepter les conditions d\'utilisation');
            return;
        }

        // Wait for Supabase API to be ready
        if (!window.supabaseAPI || !window.supabaseAPI.isReady()) {
            console.log('⏳ Waiting for Supabase API...');
            await waitForSupabaseAPI();
        }

        // Real Supabase registration
        const result = await window.supabaseAPI.register({
            firstName,
            lastName,
            email,
            password
        });

        if (result.success) {
            if (result.needsConfirmation) {
                showSuccessMessage('Compte créé ! Vérifiez votre email pour confirmer votre inscription.');
            } else {
                showSuccessMessage('Compte créé avec succès ! Redirection vers la connexion...');
                setTimeout(() => {
                    window.location.href = 'login-modern.html';
                }, 2000);
            }
        }

    } catch (error) {
        console.error('❌ Register error:', error);
        showErrorMessage(error.message || 'Erreur lors de la création du compte. Veuillez réessayer.');
    } finally {
        setLoadingState(submitBtn, btnText, btnSpinner, false);
    }
}

// Wait for Supabase API to be ready
async function waitForSupabaseAPI() {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max

    while (attempts < maxAttempts) {
        if (window.supabaseAPI && window.supabaseAPI.isReady()) {
            return true;
        }

        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
    }

    throw new Error('Supabase API not available');
}

// Initialize password toggle functionality
function initializePasswordToggle() {
    console.log('👁️ Initializing Password Toggle...');
    const passwordToggles = document.querySelectorAll('.password-toggle');

    passwordToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const passwordInput = this.parentElement.querySelector('input[type="password"], input[type="text"]');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
                console.log('👁️ Password shown');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
                console.log('👁️ Password hidden');
            }
        });
    });

    console.log('✅ Password toggle initialized');
}

// Initialize demo accounts functionality
function initializeDemoAccounts() {
    console.log('🎭 Initializing Demo Accounts...');
    const demoAccounts = document.querySelectorAll('.demo-account');

    demoAccounts.forEach(account => {
        account.addEventListener('click', function() {
            const email = this.dataset.email;
            const password = this.dataset.password;

            console.log(`🎭 Demo account selected: ${email}`);

            // Fill form fields
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            if (emailInput && passwordInput) {
                emailInput.value = email;
                passwordInput.value = password;

                // Add visual feedback
                this.style.background = 'var(--primary-50)';
                this.style.borderColor = 'var(--primary-300)';

                setTimeout(() => {
                    this.style.background = '';
                    this.style.borderColor = '';
                }, 1000);

                // Show success message
                showInfoMessage(`Identifiants remplis pour ${email}`);
            }
        });
    });

    console.log('✅ Demo accounts initialized');
}

// Initialize form validation
function initializeFormValidation() {
    console.log('✅ Initializing Form Validation...');
    const inputs = document.querySelectorAll('.form-input');

    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            // Clear error on input
            const errorElement = document.getElementById(`${this.id}-error`);
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        });
    });

    console.log('✅ Form validation initialized');
}

// Initialize animations
function initializeAnimations() {
    console.log('✨ Initializing Auth Animations...');

    // Add entrance animations
    const authCard = document.querySelector('.auth-card');
    const authBranding = document.querySelector('.auth-branding');

    if (authCard) {
        authCard.style.opacity = '0';
        authCard.style.transform = 'translateY(30px)';

        setTimeout(() => {
            authCard.style.transition = 'all 0.6s ease-out';
            authCard.style.opacity = '1';
            authCard.style.transform = 'translateY(0)';
        }, 200);
    }

    if (authBranding) {
        authBranding.style.opacity = '0';
        authBranding.style.transform = 'translateX(-30px)';

        setTimeout(() => {
            authBranding.style.transition = 'all 0.6s ease-out';
            authBranding.style.opacity = '1';
            authBranding.style.transform = 'translateX(0)';
        }, 400);
    }

    console.log('✅ Auth animations initialized');
}

// Validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    // At least 6 characters for login, 8 for registration with complexity
    const isLogin = document.getElementById('loginForm');
    if (isLogin) {
        return password && password.length >= 6;
    } else {
        // Registration - more complex validation
        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return passwordRegex.test(password);
    }
}

function validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;

    switch (fieldName) {
        case 'email':
            if (!validateEmail(value)) {
                showFieldError(field.id, 'Adresse email invalide');
                return false;
            }
            break;
        case 'password':
            if (!validatePassword(value)) {
                const message = document.getElementById('loginForm')
                    ? 'Mot de passe trop court (min. 6 caractères)'
                    : 'Mot de passe trop faible (min. 8 caractères, majuscules, minuscules, chiffres)';
                showFieldError(field.id, message);
                return false;
            }
            break;
        case 'firstName':
        case 'lastName':
            if (value.length < 2) {
                showFieldError(field.id, 'Ce champ doit contenir au moins 2 caractères');
                return false;
            }
            break;
    }

    return true;
}

// Utility functions
function setLoadingState(button, textElement, spinnerElement, isLoading) {
    if (isLoading) {
        button.disabled = true;
        if (textElement) textElement.style.opacity = '0';
        if (spinnerElement) spinnerElement.classList.remove('hidden');
    } else {
        button.disabled = false;
        if (textElement) textElement.style.opacity = '1';
        if (spinnerElement) spinnerElement.classList.add('hidden');
    }
}

function showFieldError(fieldId, message) {
    const errorElement = document.getElementById(`${fieldId}-error`);
    if (errorElement) {
        errorElement.textContent = message;
        errorElement.classList.add('show');
        console.log(`❌ Field error: ${fieldId} - ${message}`);
    }
}

function clearFormErrors() {
    const errorElements = document.querySelectorAll('.form-error');
    errorElements.forEach(error => {
        error.classList.remove('show');
    });
}

function showSuccessMessage(message) {
    showToast(message, 'success');
}

function showErrorMessage(message) {
    showToast(message, 'error');
}

function showInfoMessage(message) {
    showToast(message, 'info');
}

function showToast(message, type = 'info') {
    console.log(`🍞 Toast: ${message} (${type})`);

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getToastColor(type)};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
        max-width: 400px;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

function getToastColor(type) {
    switch (type) {
        case 'success': return '#10b981';
        case 'error': return '#ef4444';
        case 'warning': return '#f59e0b';
        default: return '#3b82f6';
    }
}

// Handle Google login
function handleGoogleLogin() {
    console.log('🔍 Google login clicked');
    showInfoMessage('Connexion Google en cours de développement...');
}

// Export functions for external use
window.ModernAuth = {
    validateEmail,
    validatePassword,
    showSuccessMessage,
    showErrorMessage,
    showInfoMessage
};

console.log('🔐 Modern Authentication JavaScript with Supabase Integration Loaded Successfully');