// Modern Landing Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Modern Landing Page Loading...');
    initializeNavigation();
    initializeScrollEffects();
    initializeAnimations();
    initializeMobileMenu();
    initializeInteractiveElements();
    loadInvestmentPlans();
    console.log('✅ Modern Landing Page Loaded Successfully');
});

// Navigation functionality
function initializeNavigation() {
    console.log('🧭 Initializing Navigation...');
    const navbar = document.getElementById('navbar');
    const navLinks = document.querySelectorAll('.navbar-link');
    const brandLink = document.getElementById('brand-link');

    // Brand link functionality
    if (brandLink) {
        brandLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            console.log('🏠 Brand clicked - scrolling to top');
        });
    }

    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            console.log(`🔗 Navigation clicked: ${targetId}`);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });

                // Update active link
                updateActiveNavLink(this);
                console.log(`✅ Scrolled to section: ${targetId}`);
            } else {
                console.warn(`⚠️ Section not found: ${targetId}`);
            }
        });
    });

    // Update active navigation on scroll
    window.addEventListener('scroll', function() {
        updateNavbarOnScroll();
        updateActiveNavOnScroll();
    });

    console.log('✅ Navigation initialized');
}

function updateActiveNavLink(activeLink) {
    const navLinks = document.querySelectorAll('.navbar-link');
    navLinks.forEach(link => link.classList.remove('active'));
    activeLink.classList.add('active');
    console.log(`🎯 Active nav updated: ${activeLink.textContent}`);
}

function updateNavbarOnScroll() {
    const navbar = document.getElementById('navbar');
    const scrollTop = window.pageYOffset;

    if (scrollTop > 50) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
}

function updateActiveNavOnScroll() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.navbar-link');
    const scrollTop = window.pageYOffset + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollTop >= sectionTop && scrollTop < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionId}`) {
                    link.classList.add('active');
                }
            });
        }
    });
}

// Mobile menu functionality
function initializeMobileMenu() {
    console.log('📱 Initializing Mobile Menu...');
    const mobileToggle = document.getElementById('mobile-toggle');
    const navbarNav = document.getElementById('navbar-nav');

    if (mobileToggle && navbarNav) {
        mobileToggle.addEventListener('click', function() {
            navbarNav.classList.toggle('active');
            mobileToggle.classList.toggle('active');
            console.log('📱 Mobile menu toggled');
        });

        // Close mobile menu when clicking on a link
        const navLinks = navbarNav.querySelectorAll('.navbar-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navbarNav.classList.remove('active');
                mobileToggle.classList.remove('active');
                console.log('📱 Mobile menu closed');
            });
        });

        console.log('✅ Mobile menu initialized');
    } else {
        console.warn('⚠️ Mobile menu elements not found');
    }
}

// Interactive elements
function initializeInteractiveElements() {
    console.log('⚡ Initializing Interactive Elements...');

    // Demo button functionality
    const demoBtn = document.getElementById('demo-btn');
    if (demoBtn) {
        demoBtn.addEventListener('click', function() {
            console.log('🎬 Demo button clicked');
            showToast('Démo vidéo bientôt disponible !', 'info');
        });
    }

    // Plan buttons functionality
    const planButtons = document.querySelectorAll('.plan-btn');
    planButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const planType = this.dataset.plan;
            console.log(`💰 Plan selected: ${planType}`);
            showToast(`Plan ${planType} sélectionné ! Redirection vers l'inscription...`, 'success');
            setTimeout(() => {
                window.location.href = 'register-modern.html';
            }, 1500);
        });
    });

    // Contact form functionality
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('📧 Contact form submitted');

            const formData = new FormData(this);
            const name = formData.get('name');
            const email = formData.get('email');
            const subject = formData.get('subject');
            const message = formData.get('message');

            // Simulate form submission
            showToast('Message envoyé avec succès ! Nous vous répondrons sous 24h.', 'success');
            this.reset();
        });
    }

    console.log('✅ Interactive elements initialized');
}

// Scroll effects and animations
function initializeScrollEffects() {
    console.log('🎨 Initializing Scroll Effects...');

    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in');
                console.log(`🎭 Element animated: ${entry.target.className}`);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.feature-card, .hero-visual, .section-header, .plan-card');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    console.log('✅ Scroll effects initialized');
}

// Initialize animations
function initializeAnimations() {
    console.log('✨ Initializing Animations...');

    // Animate hero stats on load
    setTimeout(() => {
        animateCounters();
    }, 1000);

    // Parallax effect for hero background
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        const heroPattern = document.querySelector('.hero-pattern');

        if (heroPattern) {
            heroPattern.style.transform = `translateY(${scrolled * 0.5}px)`;
        }
    });

    console.log('✅ Animations initialized');
}

function animateCounters() {
    const counters = document.querySelectorAll('.stat-value');

    counters.forEach(counter => {
        const target = counter.textContent;
        const isNumber = /^\d+/.test(target);

        if (isNumber) {
            const finalNumber = parseInt(target.replace(/[^\d]/g, ''));
            const suffix = target.replace(/[\d,]/g, '');
            let current = 0;
            const increment = finalNumber / 50;

            const timer = setInterval(() => {
                current += increment;
                if (current >= finalNumber) {
                    current = finalNumber;
                    clearInterval(timer);
                }

                counter.textContent = Math.floor(current).toLocaleString() + suffix;
            }, 30);
        }
    });
}

// Load investment plans dynamically
async function loadInvestmentPlans() {
    console.log('💰 Loading Investment Plans...');
    try {
        // Wait for API to be available
        if (typeof window.supabaseAPI === 'undefined') {
            console.log('⏳ Waiting for API to load...');
            setTimeout(loadInvestmentPlans, 1000);
            return;
        }

        const plans = await window.supabaseAPI.getInvestmentPlans();

        if (plans && plans.length > 0) {
            console.log(`✅ Loaded ${plans.length} investment plans`);
            // You can add code here to display plans if needed
        }
    } catch (error) {
        console.error('❌ Error loading investment plans:', error);
    }
}

// Toast notification system
function showToast(message, type = 'info') {
    console.log(`🍞 Toast: ${message} (${type})`);

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${getToastColor(type)};
        color: white;
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease-out;
        max-width: 400px;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
    `;

    document.body.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);

    // Remove after delay
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 4000);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        default: return 'fa-info-circle';
    }
}

function getToastColor(type) {
    switch (type) {
        case 'success': return '#10b981';
        case 'error': return '#ef4444';
        case 'warning': return '#f59e0b';
        default: return '#3b82f6';
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add smooth reveal animations
function addRevealAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        .animate-fade-in {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .feature-card, .plan-card {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .feature-card.animate-fade-in, .plan-card.animate-fade-in {
            opacity: 1;
            transform: translateY(0);
        }

        .hero-visual {
            opacity: 0;
            transform: translateX(50px);
            transition: all 0.8s ease-out;
        }

        .hero-visual.animate-fade-in {
            opacity: 1;
            transform: translateX(0);
        }

        /* Mobile menu styles */
        .navbar-nav {
            transition: all 0.3s ease-out;
        }

        @media (max-width: 768px) {
            .navbar-nav {
                position: fixed;
                top: 80px;
                left: 0;
                right: 0;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 20px;
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            }

            .navbar-nav.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .mobile-menu-toggle.active span:nth-child(1) {
                transform: rotate(45deg) translate(5px, 5px);
            }

            .mobile-menu-toggle.active span:nth-child(2) {
                opacity: 0;
            }

            .mobile-menu-toggle.active span:nth-child(3) {
                transform: rotate(-45deg) translate(7px, -6px);
            }
        }
    `;
    document.head.appendChild(style);
}

// Initialize reveal animations
addRevealAnimations();

// Performance optimization
const debouncedScroll = debounce(() => {
    updateNavbarOnScroll();
    updateActiveNavOnScroll();
}, 10);

window.addEventListener('scroll', debouncedScroll);

// Add loading state management
window.addEventListener('load', function() {
    document.body.classList.add('loaded');
    console.log('🎉 Page fully loaded');

    // Trigger initial animations
    setTimeout(() => {
        const heroElements = document.querySelectorAll('.hero-text > *');
        heroElements.forEach((el, index) => {
            setTimeout(() => {
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }, 300);
});

// Add initial styles for loading animation
const initialStyles = document.createElement('style');
initialStyles.textContent = `
    .hero-text > * {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }

    body.loaded .hero-text > * {
        opacity: 1;
        transform: translateY(0);
    }
`;
document.head.appendChild(initialStyles);

// Export functions for external use
window.CryptoBoostLanding = {
    updateActiveNavLink,
    animateCounters,
    loadInvestmentPlans,
    showToast
};

console.log('🚀 Modern Landing JavaScript Loaded Successfully');