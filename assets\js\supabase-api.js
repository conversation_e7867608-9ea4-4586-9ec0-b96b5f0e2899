// CryptoBoost Supabase API - Complete Implementation
console.log('🔄 Loading Supabase API...');

class SupabaseAPI {
    constructor() {
        // Validate dependencies
        if (typeof window.supabaseConfig === 'undefined') {
            throw new Error('supabaseConfig is not available. Ensure config.js is loaded first.');
        }
        if (typeof window.apiEndpoints === 'undefined') {
            throw new Error('apiEndpoints is not available. Ensure config.js is loaded first.');
        }

        this.config = window.supabaseConfig;
        this.endpoints = window.apiEndpoints;

        console.log('✅ SupabaseAPI initialized successfully');
        console.log('📡 Supabase URL:', this.config.url);
        console.log('🔗 Available endpoints:', Object.keys(this.endpoints));
    }

    // Core HTTP request method with comprehensive error handling
    async request(url, options = {}) {
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'apikey': this.config.anon<PERSON><PERSON>,
            'Authorization': `Bearer ${this.config.anonKey}`,
            'Prefer': 'return=representation'
        };

        const requestConfig = {
            headers: { ...defaultHeaders, ...options.headers },
            ...options
        };

        // Add timeout protection
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        try {
            console.log(`🔗 API Request: ${options.method || 'GET'} ${url}`);

            const response = await fetch(url, {
                ...requestConfig,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                const errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                console.error(`❌ API Error: ${errorMessage}`, errorText);
                throw new Error(`${errorMessage} - ${errorText}`);
            }

            const data = await response.json();
            const recordCount = Array.isArray(data) ? data.length : 'N/A';
            console.log(`✅ API Success: ${recordCount} records returned`);

            return data;
        } catch (error) {
            clearTimeout(timeoutId);

            if (error.name === 'AbortError') {
                const timeoutError = 'Request timeout after 15 seconds';
                console.error(`⏰ ${timeoutError}`);
                throw new Error(timeoutError);
            }

            console.error('❌ API Request failed:', error);
            throw error;
        }
    }

    // Basic CRUD Operations
    async get(table, filters = {}) {
        let url = this.endpoints[table];

        if (!url) {
            throw new Error(`Unknown table: ${table}. Available tables: ${Object.keys(this.endpoints).join(', ')}`);
        }

        // Add filters as query parameters
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${encodeURIComponent(value)}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, { method: 'GET' });
    }

    async post(table, data) {
        const url = this.endpoints[table];
        if (!url) {
            throw new Error(`Unknown table: ${table}`);
        }

        return await this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async patch(table, filters, data) {
        let url = this.endpoints[table];
        if (!url) {
            throw new Error(`Unknown table: ${table}`);
        }

        // Add filters for update condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${encodeURIComponent(value)}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    async delete(table, filters) {
        let url = this.endpoints[table];
        if (!url) {
            throw new Error(`Unknown table: ${table}`);
        }

        // Add filters for delete condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${encodeURIComponent(value)}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, { method: 'DELETE' });
    }

    // User Management Methods
    async getUserByEmail(email) {
        console.log(`🔍 Getting user by email: ${email}`);
        return await this.get('users', { email: email });
    }

    async createUser(userData) {
        console.log('👤 Creating new user:', userData.email);
        return await this.post('users', userData);
    }

    async updateUser(email, userData) {
        console.log(`📝 Updating user: ${email}`);
        return await this.patch('users', { email: email }, userData);
    }

    async deleteUser(email) {
        console.log(`🗑️ Deleting user: ${email}`);
        return await this.delete('users', { email: email });
    }

    // Investment Plans Methods
    async getInvestmentPlans() {
        console.log('📊 Getting investment plans');
        return await this.get('plans');
    }

    // Transaction Methods
    async getUserTransactions(userEmail) {
        console.log(`💳 Getting transactions for user: ${userEmail}`);
        try {
            const result = await this.get('transactions', { user_email: userEmail });
            console.log(`💳 Found ${result.length} transactions for ${userEmail}`);
            return result;
        } catch (error) {
            console.error(`💳 Error getting transactions for ${userEmail}:`, error);
            // Fallback: get all transactions and filter client-side
            try {
                console.log('💳 Trying fallback: get all transactions and filter...');
                const allTransactions = await this.get('transactions');
                const userTransactions = allTransactions.filter(tx => tx.user_email === userEmail);
                console.log(`💳 Fallback result: ${userTransactions.length} transactions`);
                return userTransactions;
            } catch (fallbackError) {
                console.error('💳 Fallback also failed:', fallbackError);
                throw error;
            }
        }
    }

    async createTransaction(transactionData) {
        console.log('💳 Creating transaction:', transactionData.type);
        return await this.post('transactions', transactionData);
    }

    // Company Wallets Methods
    async getCompanyWallets() {
        console.log('🏦 Getting company wallets');
        return await this.get('wallets');
    }

    // Configuration Methods
    async getConfig() {
        console.log('⚙️ Getting configuration');
        return await this.get('config');
    }

    // Utility Methods
    async testConnection() {
        console.log('🔍 Testing Supabase connection...');
        try {
            const response = await fetch(`${this.config.url}/rest/v1/`, {
                headers: {
                    'apikey': this.config.anonKey,
                    'Authorization': `Bearer ${this.config.anonKey}`
                }
            });

            if (response.ok) {
                console.log('✅ Supabase connection successful');
                return true;
            } else {
                console.error('❌ Supabase connection failed:', response.status, response.statusText);
                return false;
            }
        } catch (error) {
            console.error('❌ Supabase connection error:', error);
            return false;
        }
    }
}

// CRITICAL: Global Initialization
console.log('🚀 Initializing global supabaseAPI...');

// Function to safely initialize the API
function initializeSupabaseAPI() {
    try {
        // Check if dependencies are available
        if (typeof window.supabaseConfig === 'undefined') {
            console.warn('⚠️ supabaseConfig not available yet');
            return false;
        }

        if (typeof window.apiEndpoints === 'undefined') {
            console.warn('⚠️ apiEndpoints not available yet');
            return false;
        }

        // Create the global instance
        const supabaseAPI = new SupabaseAPI();
        window.supabaseAPI = supabaseAPI;

        // Also make it available as a global variable (not just window property)
        if (typeof globalThis !== 'undefined') {
            globalThis.supabaseAPI = supabaseAPI;
        }

        console.log('🎉 supabaseAPI successfully initialized and made global!');
        console.log('🔍 Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(supabaseAPI)).filter(name => name !== 'constructor'));

        // Test the connection immediately
        supabaseAPI.testConnection().then(success => {
            if (success) {
                console.log('✅ Supabase connection verified');
            } else {
                console.error('❌ Supabase connection failed');
            }
        });

        return true;
    } catch (error) {
        console.error('❌ Failed to initialize supabaseAPI:', error);
        return false;
    }
}

// Multiple initialization strategies to ensure it works
console.log('📋 Attempting immediate initialization...');
if (!initializeSupabaseAPI()) {
    console.log('📋 Immediate initialization failed, setting up fallbacks...');

    // Strategy 1: DOM Content Loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📋 DOM loaded, attempting initialization...');
            setTimeout(initializeSupabaseAPI, 100);
        });
    }

    // Strategy 2: Window Load
    if (document.readyState !== 'complete') {
        window.addEventListener('load', () => {
            console.log('📋 Window loaded, attempting initialization...');
            setTimeout(initializeSupabaseAPI, 200);
        });
    }

    // Strategy 3: Delayed attempts
    setTimeout(() => {
        console.log('📋 Delayed attempt 1...');
        if (!window.supabaseAPI) {
            initializeSupabaseAPI();
        }
    }, 500);

    setTimeout(() => {
        console.log('📋 Delayed attempt 2...');
        if (!window.supabaseAPI) {
            initializeSupabaseAPI();
        }
    }, 1000);

    setTimeout(() => {
        console.log('📋 Final delayed attempt...');
        if (!window.supabaseAPI) {
            initializeSupabaseAPI();
        }
    }, 2000);
}

console.log('✅ Supabase API module loaded successfully!');