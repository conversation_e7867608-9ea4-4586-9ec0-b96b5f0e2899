// Supabase API Helper Functions

class SupabaseAPI {
    constructor() {
        this.config = supabaseConfig;
        this.endpoints = apiEndpoints;
    }

    // Generic HTTP request method
    async request(url, options = {}) {
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'apikey': this.config.anon<PERSON>ey,
            'Authorization': `Bearer ${this.config.anonKey}`
        };

        const config = {
            headers: { ...defaultHeaders, ...options.headers },
            ...options
        };

        try {
            // Add timeout to prevent hanging requests
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
            }

            const data = await response.json();
            return data;
        } catch (error) {
            if (error.name === 'AbortError') {
                throw new Error('Request timeout - La requête a pris trop de temps (15s)');
            }
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // GET request
    async get(table, filters = {}) {
        let url = this.endpoints[table];

        // Add filters as query parameters
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${encodeURIComponent(value)}`);
            });
            url += `?${params.toString()}`;
        }

        console.log(`API GET Request: ${url}`); // Debug log
        return await this.request(url, { method: 'GET' });
    }

    // POST request (Insert)
    async post(table, data) {
        const url = this.endpoints[table];
        return await this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PATCH request (Update)
    async patch(table, filters, data) {
        let url = this.endpoints[table];
        
        // Add filters for update condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${value}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(table, filters) {
        let url = this.endpoints[table];
        
        // Add filters for delete condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${value}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, { method: 'DELETE' });
    }

    // User-specific methods
    async getUserByEmail(email) {
        return await this.get('users', { email });
    }

    async createUser(userData) {
        return await this.post('users', userData);
    }

    async updateUser(email, userData) {
        return await this.patch('users', { email }, userData);
    }

    // Transaction methods
    async getUserTransactions(userEmail) {
        try {
            console.log(`Getting transactions for user: ${userEmail}`);

            // Try direct approach first
            const url = `${this.endpoints.transactions}?user_email=eq.${encodeURIComponent(userEmail)}`;
            console.log(`Direct URL: ${url}`);

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'apikey': this.config.anonKey,
                    'Authorization': `Bearer ${this.config.anonKey}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`Transactions result:`, result);
            return result;
        } catch (error) {
            console.error(`Error getting transactions for ${userEmail}:`, error);

            // Fallback: get all transactions and filter client-side
            try {
                console.log('Trying fallback: get all transactions and filter...');
                const allTransactions = await this.get('transactions');
                const userTransactions = allTransactions.filter(tx => tx.user_email === userEmail);
                console.log(`Fallback result: ${userTransactions.length} transactions`);
                return userTransactions;
            } catch (fallbackError) {
                console.error('Fallback also failed:', fallbackError);
                throw error;
            }
        }
    }

    async createTransaction(transactionData) {
        return await this.post('transactions', transactionData);
    }

    async updateTransaction(id, transactionData) {
        return await this.patch('transactions', { id }, transactionData);
    }

    // Config methods
    async getConfig(type = null) {
        if (type) {
            return await this.get('config', { type });
        }
        return await this.get('config');
    }

    async updateConfig(type, configData) {
        return await this.patch('config', { type }, configData);
    }

    // Investment plans methods
    async getInvestmentPlans() {
        return await this.get('plans');
    }

    async createInvestmentPlan(planData) {
        return await this.post('plans', planData);
    }

    async updateInvestmentPlan(id, planData) {
        return await this.patch('plans', { id }, planData);
    }

    async deleteInvestmentPlan(id) {
        return await this.delete('plans', { id });
    }

    // Company wallets methods
    async getCompanyWallets() {
        return await this.get('wallets');
    }

    async createCompanyWallet(walletData) {
        return await this.post('wallets', walletData);
    }

    async updateCompanyWallet(id, walletData) {
        return await this.patch('wallets', { id }, walletData);
    }

    async deleteCompanyWallet(id) {
        return await this.delete('wallets', { id });
    }
}

// Create global instance
const supabaseAPI = new SupabaseAPI();

// Backward compatibility aliases for existing code
const configApi = {
    get: async (query) => {
        if (query === '?action=plan_config') {
            return await supabaseAPI.getInvestmentPlans();
        }
        return await supabaseAPI.getConfig();
    }
};

const dataApi = {
    get: async (filters) => {
        if (filters.email) {
            return await supabaseAPI.getUserByEmail(filters.email);
        }
        return await supabaseAPI.get('users', filters);
    },
    post: async (data) => {
        return await supabaseAPI.createUser(data);
    },
    patch: async (filters, data) => {
        if (filters.email) {
            return await supabaseAPI.updateUser(filters.email, data);
        }
        return await supabaseAPI.patch('users', filters, data);
    }
};
