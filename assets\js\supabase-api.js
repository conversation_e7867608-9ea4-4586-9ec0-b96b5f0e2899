// CryptoBoost Supabase API Integration
// Real database integration with Supabase

class CryptoBoostAPI {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.isInitialized = false;
        this.initializeSupabase();
    }

    // Initialize Supabase client
    async initializeSupabase() {
        try {
            // Wait for config to be loaded
            if (typeof window.supabaseConfig === 'undefined') {
                console.log('⏳ Waiting for config to load...');
                setTimeout(() => this.initializeSupabase(), 100);
                return;
            }

            // Import Supabase from CDN
            if (typeof window.supabase === 'undefined') {
                await this.loadSupabaseSDK();
            }

            // Initialize Supabase client
            this.supabase = window.supabase.createClient(
                window.supabaseConfig.url,
                window.supabaseConfig.anonKey
            );

            this.isInitialized = true;
            console.log('✅ Supabase client initialized successfully');

            // Check for existing session
            await this.checkSession();

        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            this.isInitialized = false;
        }
    }

    // Load Supabase SDK from CDN
    async loadSupabaseSDK() {
        return new Promise((resolve, reject) => {
            if (typeof window.supabase !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2';
            script.onload = () => {
                console.log('✅ Supabase SDK loaded');
                resolve();
            };
            script.onerror = () => {
                console.error('❌ Failed to load Supabase SDK');
                reject(new Error('Failed to load Supabase SDK'));
            };
            document.head.appendChild(script);
        });
    }

    // Check for existing session
    async checkSession() {
        try {
            const { data: { session }, error } = await this.supabase.auth.getSession();

            if (error) {
                console.error('❌ Session check error:', error);
                return null;
            }

            if (session) {
                this.currentUser = session.user;
                console.log('✅ User session found:', this.currentUser.email);
                return session.user;
            }

            console.log('ℹ️ No active session found');
            return null;
        } catch (error) {
            console.error('❌ Session check failed:', error);
            return null;
        }
    }

    // Wait for initialization
    async waitForInit() {
        while (!this.isInitialized) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    // ===== AUTHENTICATION METHODS =====

    // User login
    async login(email, password) {
        await this.waitForInit();

        try {
            console.log(`🔐 Attempting login for: ${email}`);

            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email,
                password: password
            });

            if (error) {
                console.error('❌ Login error:', error);
                throw new Error(this.getErrorMessage(error));
            }

            if (data.user) {
                this.currentUser = data.user;
                console.log('✅ Login successful:', data.user.email);

                // Get user profile data
                const userProfile = await this.getUserProfile(data.user.id);

                return {
                    success: true,
                    user: data.user,
                    profile: userProfile,
                    session: data.session
                };
            }

            throw new Error('Échec de la connexion');

        } catch (error) {
            console.error('❌ Login failed:', error);
            throw error;
        }
    }

    // User registration
    async register(userData) {
        await this.waitForInit();

        try {
            console.log(`📝 Attempting registration for: ${userData.email}`);

            const { data, error } = await this.supabase.auth.signUp({
                email: userData.email,
                password: userData.password,
                options: {
                    data: {
                        first_name: userData.firstName,
                        last_name: userData.lastName,
                        full_name: `${userData.firstName} ${userData.lastName}`
                    }
                }
            });

            if (error) {
                console.error('❌ Registration error:', error);
                throw new Error(this.getErrorMessage(error));
            }

            if (data.user) {
                console.log('✅ Registration successful:', data.user.email);

                // Create user profile
                await this.createUserProfile(data.user, userData);

                return {
                    success: true,
                    user: data.user,
                    needsConfirmation: !data.session
                };
            }

            throw new Error('Échec de l\'inscription');

        } catch (error) {
            console.error('❌ Registration failed:', error);
            throw error;
        }
    }

    // User logout
    async logout() {
        await this.waitForInit();

        try {
            const { error } = await this.supabase.auth.signOut();

            if (error) {
                console.error('❌ Logout error:', error);
                throw new Error(this.getErrorMessage(error));
            }

            this.currentUser = null;
            console.log('✅ Logout successful');

            return { success: true };

        } catch (error) {
            console.error('❌ Logout failed:', error);
            throw error;
        }
    }

    // ===== USER PROFILE METHODS =====

    // Get user profile
    async getUserProfile(userId) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single();

            if (error) {
                console.error('❌ Get profile error:', error);
                return null;
            }

            console.log('✅ User profile retrieved');
            return data;

        } catch (error) {
            console.error('❌ Failed to get user profile:', error);
            return null;
        }
    }

    // Create user profile
    async createUserProfile(user, userData) {
        await this.waitForInit();

        try {
            const profileData = {
                id: user.id,
                email: user.email,
                first_name: userData.firstName,
                last_name: userData.lastName,
                full_name: `${userData.firstName} ${userData.lastName}`,
                role: 'user',
                status: 'active',
                balance: 0,
                created_at: new Date().toISOString()
            };

            const { data, error } = await this.supabase
                .from('users')
                .insert([profileData])
                .select()
                .single();

            if (error) {
                console.error('❌ Create profile error:', error);
                throw new Error('Erreur lors de la création du profil');
            }

            console.log('✅ User profile created');
            return data;

        } catch (error) {
            console.error('❌ Failed to create user profile:', error);
            throw error;
        }
    }

    // Update user profile
    async updateUserProfile(userId, updates) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('users')
                .update(updates)
                .eq('id', userId)
                .select()
                .single();

            if (error) {
                console.error('❌ Update profile error:', error);
                throw new Error('Erreur lors de la mise à jour du profil');
            }

            console.log('✅ User profile updated');
            return data;

        } catch (error) {
            console.error('❌ Failed to update user profile:', error);
            throw error;
        }
    }

    // ===== PORTFOLIO METHODS =====

    // Get user portfolio
    async getUserPortfolio(userId) {
        await this.waitForInit();

        try {
            // Get user balance and basic info
            const { data: userData, error: userError } = await this.supabase
                .from('users')
                .select('balance, total_invested, total_profit')
                .eq('id', userId)
                .single();

            if (userError) {
                console.error('❌ Get portfolio error:', userError);
                throw new Error('Erreur lors de la récupération du portefeuille');
            }

            // Get user transactions for portfolio calculation
            const { data: transactions, error: transError } = await this.supabase
                .from('transactions')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (transError) {
                console.error('❌ Get transactions error:', transError);
            }

            // Calculate portfolio metrics
            const portfolio = this.calculatePortfolioMetrics(userData, transactions || []);

            console.log('✅ Portfolio data retrieved');
            return portfolio;

        } catch (error) {
            console.error('❌ Failed to get portfolio:', error);
            throw error;
        }
    }

    // Calculate portfolio metrics
    calculatePortfolioMetrics(userData, transactions) {
        const totalBalance = userData.balance || 0;
        const totalInvested = userData.total_invested || 0;
        const totalProfit = userData.total_profit || 0;

        // Calculate daily change (simplified)
        const dailyProfit = transactions
            .filter(t => {
                const transDate = new Date(t.created_at);
                const today = new Date();
                return transDate.toDateString() === today.toDateString() && t.type === 'profit';
            })
            .reduce((sum, t) => sum + (t.amount || 0), 0);

        const dailyChange = totalBalance > 0 ? (dailyProfit / totalBalance) * 100 : 0;

        // Mock asset distribution (in real app, this would come from actual holdings)
        const assets = [
            { name: 'Bitcoin', symbol: 'BTC', value: totalBalance * 0.45, change: 2.4 },
            { name: 'Ethereum', symbol: 'ETH', value: totalBalance * 0.30, change: -1.2 },
            { name: 'Cardano', symbol: 'ADA', value: totalBalance * 0.25, change: 5.7 }
        ];

        return {
            totalBalance,
            totalInvested,
            totalProfit,
            dailyProfit,
            dailyChange,
            assets,
            profitPercentage: totalInvested > 0 ? (totalProfit / totalInvested) * 100 : 0
        };
    }

    // ===== TRANSACTION METHODS =====

    // Get user transactions
    async getUserTransactions(userId, limit = 50) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('transactions')
                .select('*')
                .eq('user_id', userId)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                console.error('❌ Get transactions error:', error);
                throw new Error('Erreur lors de la récupération des transactions');
            }

            console.log('✅ Transactions retrieved');
            return data || [];

        } catch (error) {
            console.error('❌ Failed to get transactions:', error);
            throw error;
        }
    }

    // Create transaction
    async createTransaction(transactionData) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('transactions')
                .insert([{
                    ...transactionData,
                    created_at: new Date().toISOString()
                }])
                .select()
                .single();

            if (error) {
                console.error('❌ Create transaction error:', error);
                throw new Error('Erreur lors de la création de la transaction');
            }

            console.log('✅ Transaction created');
            return data;

        } catch (error) {
            console.error('❌ Failed to create transaction:', error);
            throw error;
        }
    }

    // ===== INVESTMENT PLANS METHODS =====

    // Get investment plans
    async getInvestmentPlans() {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('investment_plans')
                .select('*')
                .eq('status', 'active')
                .order('min_amount', { ascending: true });

            if (error) {
                console.error('❌ Get plans error:', error);
                throw new Error('Erreur lors de la récupération des plans');
            }

            console.log('✅ Investment plans retrieved');
            return data || [];

        } catch (error) {
            console.error('❌ Failed to get investment plans:', error);
            throw error;
        }
    }

    // ===== ADMIN METHODS =====

    // Get admin dashboard metrics
    async getAdminMetrics() {
        await this.waitForInit();

        try {
            // Get user count
            const { count: userCount, error: userError } = await this.supabase
                .from('users')
                .select('*', { count: 'exact', head: true });

            if (userError) {
                console.error('❌ Get user count error:', userError);
            }

            // Get total deposits
            const { data: deposits, error: depositError } = await this.supabase
                .from('transactions')
                .select('amount')
                .eq('type', 'deposit')
                .eq('status', 'completed');

            if (depositError) {
                console.error('❌ Get deposits error:', depositError);
            }

            // Get total withdrawals
            const { data: withdrawals, error: withdrawalError } = await this.supabase
                .from('transactions')
                .select('amount')
                .eq('type', 'withdrawal')
                .eq('status', 'completed');

            if (withdrawalError) {
                console.error('❌ Get withdrawals error:', withdrawalError);
            }

            // Calculate metrics
            const totalDeposits = deposits?.reduce((sum, d) => sum + (d.amount || 0), 0) || 0;
            const totalWithdrawals = withdrawals?.reduce((sum, w) => sum + (w.amount || 0), 0) || 0;
            const totalProfit = totalDeposits - totalWithdrawals;

            // Get active users (logged in last 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const { count: activeUsers, error: activeError } = await this.supabase
                .from('users')
                .select('*', { count: 'exact', head: true })
                .gte('last_login', thirtyDaysAgo.toISOString());

            if (activeError) {
                console.error('❌ Get active users error:', activeError);
            }

            const metrics = {
                totalUsers: userCount || 0,
                totalDeposits,
                totalWithdrawals,
                totalProfit,
                activeUsers: activeUsers || 0,
                dailyVolume: totalDeposits * 0.1 // Simplified calculation
            };

            console.log('✅ Admin metrics retrieved');
            return metrics;

        } catch (error) {
            console.error('❌ Failed to get admin metrics:', error);
            throw error;
        }
    }

    // Get recent activity for admin
    async getRecentActivity(limit = 20) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('transactions')
                .select(`
                    *,
                    users (
                        email,
                        full_name
                    )
                `)
                .order('created_at', { ascending: false })
                .limit(limit);

            if (error) {
                console.error('❌ Get recent activity error:', error);
                throw new Error('Erreur lors de la récupération de l\'activité');
            }

            console.log('✅ Recent activity retrieved');
            return data || [];

        } catch (error) {
            console.error('❌ Failed to get recent activity:', error);
            throw error;
        }
    }

    // ===== UTILITY METHODS =====

    // Get error message in French
    getErrorMessage(error) {
        const errorMessages = {
            'Invalid login credentials': 'Email ou mot de passe incorrect',
            'Email not confirmed': 'Veuillez confirmer votre email',
            'User already registered': 'Cette adresse email est déjà utilisée',
            'Password should be at least 6 characters': 'Le mot de passe doit contenir au moins 6 caractères',
            'Invalid email': 'Adresse email invalide',
            'Network error': 'Erreur de connexion réseau',
            'Database error': 'Erreur de base de données'
        };

        return errorMessages[error.message] || error.message || 'Une erreur est survenue';
    }

    // Check if user is admin
    async isAdmin(userId) {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('role')
                .eq('id', userId)
                .single();

            if (error) {
                console.error('❌ Check admin error:', error);
                return false;
            }

            return data?.role === 'admin';

        } catch (error) {
            console.error('❌ Failed to check admin status:', error);
            return false;
        }
    }

    // Test database connection
    async testConnection() {
        await this.waitForInit();

        try {
            const { data, error } = await this.supabase
                .from('users')
                .select('count', { count: 'exact', head: true });

            if (error) {
                console.error('❌ Connection test failed:', error);
                return false;
            }

            console.log('✅ Database connection successful');
            return true;

        } catch (error) {
            console.error('❌ Connection test error:', error);
            return false;
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if initialized
    isReady() {
        return this.isInitialized;
    }
}

// Initialize global API instance
window.supabaseAPI = new CryptoBoostAPI();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CryptoBoostAPI;
}

console.log('🚀 CryptoBoost Supabase API loaded successfully');