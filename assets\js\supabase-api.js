// Supabase API Helper Functions

class SupabaseAPI {
    constructor() {
        this.config = supabaseConfig;
        this.endpoints = apiEndpoints;
    }

    // Generic HTTP request method
    async request(url, options = {}) {
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'apikey': this.config.anon<PERSON>ey,
            'Authorization': `Bearer ${this.config.anonKey}`
        };

        const config = {
            headers: { ...defaultHeaders, ...options.headers },
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API Request Error:', error);
            throw error;
        }
    }

    // GET request
    async get(table, filters = {}) {
        let url = this.endpoints[table];
        
        // Add filters as query parameters
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${value}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, { method: 'GET' });
    }

    // POST request (Insert)
    async post(table, data) {
        const url = this.endpoints[table];
        return await this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PATCH request (Update)
    async patch(table, filters, data) {
        let url = this.endpoints[table];
        
        // Add filters for update condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${value}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, {
            method: 'PATCH',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(table, filters) {
        let url = this.endpoints[table];
        
        // Add filters for delete condition
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams();
            Object.entries(filters).forEach(([key, value]) => {
                params.append(key, `eq.${value}`);
            });
            url += `?${params.toString()}`;
        }

        return await this.request(url, { method: 'DELETE' });
    }

    // User-specific methods
    async getUserByEmail(email) {
        return await this.get('users', { email });
    }

    async createUser(userData) {
        return await this.post('users', userData);
    }

    async updateUser(email, userData) {
        return await this.patch('users', { email }, userData);
    }

    // Transaction methods
    async getUserTransactions(userEmail) {
        return await this.get('transactions', { user_email: userEmail });
    }

    async createTransaction(transactionData) {
        return await this.post('transactions', transactionData);
    }

    async updateTransaction(id, transactionData) {
        return await this.patch('transactions', { id }, transactionData);
    }

    // Config methods
    async getConfig(type = null) {
        if (type) {
            return await this.get('config', { type });
        }
        return await this.get('config');
    }

    async updateConfig(type, configData) {
        return await this.patch('config', { type }, configData);
    }

    // Investment plans methods
    async getInvestmentPlans() {
        return await this.get('plans');
    }

    async createInvestmentPlan(planData) {
        return await this.post('plans', planData);
    }

    async updateInvestmentPlan(id, planData) {
        return await this.patch('plans', { id }, planData);
    }

    async deleteInvestmentPlan(id) {
        return await this.delete('plans', { id });
    }

    // Company wallets methods
    async getCompanyWallets() {
        return await this.get('wallets');
    }

    async createCompanyWallet(walletData) {
        return await this.post('wallets', walletData);
    }

    async updateCompanyWallet(id, walletData) {
        return await this.patch('wallets', { id }, walletData);
    }

    async deleteCompanyWallet(id) {
        return await this.delete('wallets', { id });
    }
}

// Create global instance
const supabaseAPI = new SupabaseAPI();

// Backward compatibility aliases for existing code
const configApi = {
    get: async (query) => {
        if (query === '?action=plan_config') {
            return await supabaseAPI.getInvestmentPlans();
        }
        return await supabaseAPI.getConfig();
    }
};

const dataApi = {
    get: async (filters) => {
        if (filters.email) {
            return await supabaseAPI.getUserByEmail(filters.email);
        }
        return await supabaseAPI.get('users', filters);
    },
    post: async (data) => {
        return await supabaseAPI.createUser(data);
    },
    patch: async (filters, data) => {
        if (filters.email) {
            return await supabaseAPI.updateUser(filters.email, data);
        }
        return await supabaseAPI.patch('users', filters, data);
    }
};
