-- Script SQL COMPLET pour Supabase - CryptoBoost
-- Crée les tables ET insère les données de test
-- À exécuter dans le SQL Editor de Supabase

-- ================================================
-- 1. CRÉATION DES TABLES
-- ================================================

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    balance DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des plans d'investissement
CREATE TABLE IF NOT EXISTS investment_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    investment VARCHAR(100) NOT NULL,
    description TEXT,
    roi_percentage DECIMAL(5,2),
    duration_days INTEGER,
    min_amount DECIMAL(15,2),
    max_amount DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des transactions
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_email VARCHAR(255),
    type VARCHAR(50) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'investment', 'profit')),
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
    plan_name VARCHAR(255),
    transaction_hash VARCHAR(255),
    wallet_address VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des portefeuilles d'entreprise
CREATE TABLE IF NOT EXISTS company_wallets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    currency VARCHAR(50) NOT NULL,
    address VARCHAR(255) NOT NULL,
    network VARCHAR(100),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table de configuration
CREATE TABLE IF NOT EXISTS config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ================================================
-- 2. ACTIVATION DE ROW LEVEL SECURITY (RLS)
-- ================================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE investment_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE config ENABLE ROW LEVEL SECURITY;

-- ================================================
-- 3. CRÉATION DES POLITIQUES DE SÉCURITÉ
-- ================================================

-- Politiques pour les utilisateurs
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid()::text = id::text);
CREATE POLICY "Users can update own data" ON users FOR UPDATE USING (auth.uid()::text = id::text);

-- Politiques pour les plans (lecture publique)
CREATE POLICY "Investment plans are viewable by everyone" ON investment_plans FOR SELECT USING (true);

-- Politiques pour les transactions (utilisateurs voient leurs propres transactions)
CREATE POLICY "Users can view own transactions" ON transactions FOR SELECT USING (auth.email() = user_email);
CREATE POLICY "Users can insert own transactions" ON transactions FOR INSERT WITH CHECK (auth.email() = user_email);

-- Politiques pour les portefeuilles (lecture publique)
CREATE POLICY "Company wallets are viewable by everyone" ON company_wallets FOR SELECT USING (true);

-- Politiques pour la config (lecture publique)
CREATE POLICY "Config is viewable by everyone" ON config FOR SELECT USING (true);

-- ================================================
-- 4. INSERTION DES DONNÉES DE TEST
-- ================================================

-- Utilisateur administrateur
INSERT INTO users (email, password, role, status, balance, total_invested, total_profit) 
VALUES (
    '<EMAIL>',
    'admin123',
    'admin',
    'active',
    0.00,
    0.00,
    0.00
);

-- Utilisateur normal
INSERT INTO users (email, password, role, status, balance, total_invested, total_profit) 
VALUES (
    '<EMAIL>',
    'user123',
    'user',
    'active',
    1500.00,
    5000.00,
    750.00
);

-- Plans d'investissement
INSERT INTO investment_plans (name, investment, description, roi_percentage, duration_days, min_amount, max_amount, status) 
VALUES 
    ('Plan Starter', 'Bitcoin', 'Plan d''entrée pour débuter dans l''investissement crypto', 5.0, 30, 100.0, 1000.0, 'active'),
    ('Plan Premium', 'Ethereum', 'Plan intermédiaire avec rendements attractifs', 8.0, 60, 1000.0, 10000.0, 'active'),
    ('Plan VIP', 'Portfolio Diversifié', 'Plan premium pour investisseurs expérimentés', 12.0, 90, 10000.0, 100000.0, 'active');

-- Portefeuilles d'entreprise
INSERT INTO company_wallets (currency, address, network, status) 
VALUES 
    ('Bitcoin', '**********************************', 'Bitcoin', 'active'),
    ('Ethereum', '******************************************', 'Ethereum', 'active'),
    ('USDT', '******************************************', 'Ethereum (ERC-20)', 'active'),
    ('Litecoin', 'LTC1qw508d6qejxtdg4y5r3zarvary0c5xw7kv8f3t4', 'Litecoin', 'active');

-- Configuration de l'application
INSERT INTO config (key, value, description) 
VALUES 
    ('app_name', 'CryptoBoost', 'Nom de l''application'),
    ('app_version', '2.0.0', 'Version de l''application'),
    ('maintenance_mode', 'false', 'Mode maintenance activé/désactivé'),
    ('min_deposit', '50', 'Montant minimum de dépôt en USD'),
    ('max_deposit', '100000', 'Montant maximum de dépôt en USD'),
    ('withdrawal_fee_percentage', '2.5', 'Frais de retrait en pourcentage'),
    ('support_email', '<EMAIL>', 'Email de support client'),
    ('company_address', '123 Crypto Street, Blockchain City, BC 12345', 'Adresse de l''entreprise');

-- Transactions d'exemple
INSERT INTO transactions (user_email, type, amount, status, plan_name, notes) 
VALUES 
    ('<EMAIL>', 'deposit', 1000.00, 'completed', NULL, 'Dépôt initial'),
    ('<EMAIL>', 'investment', 500.00, 'completed', 'Plan Starter', 'Premier investissement'),
    ('<EMAIL>', 'profit', 25.00, 'completed', 'Plan Starter', 'Profit généré'),
    ('<EMAIL>', 'deposit', 5000.00, 'completed', NULL, 'Dépôt administrateur');

-- ================================================
-- 5. VÉRIFICATION DES DONNÉES INSÉRÉES
-- ================================================

-- Compter les éléments dans chaque table
SELECT 'UTILISATEURS' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'PLANS D''INVESTISSEMENT' as table_name, COUNT(*) as count FROM investment_plans
UNION ALL
SELECT 'PORTEFEUILLES' as table_name, COUNT(*) as count FROM company_wallets
UNION ALL
SELECT 'CONFIGURATIONS' as table_name, COUNT(*) as count FROM config
UNION ALL
SELECT 'TRANSACTIONS' as table_name, COUNT(*) as count FROM transactions;

-- Afficher les utilisateurs créés
SELECT 
    email, 
    role, 
    status, 
    balance, 
    total_invested, 
    total_profit, 
    created_at 
FROM users 
ORDER BY created_at DESC;

-- Afficher les plans d'investissement
SELECT 
    name, 
    investment, 
    roi_percentage, 
    duration_days, 
    min_amount, 
    max_amount, 
    status 
FROM investment_plans 
ORDER BY min_amount;

-- ================================================
-- 6. MESSAGE DE CONFIRMATION
-- ================================================

SELECT 
    '🎉 CONFIGURATION SUPABASE TERMINÉE !' as message,
    'Toutes les tables ont été créées avec succès' as status,
    'Utilisez <EMAIL> / admin123 pour vous connecter' as admin_login;
