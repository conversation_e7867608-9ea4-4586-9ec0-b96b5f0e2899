<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoBoost - Tests Complets</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover {
            background: #4338ca;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .loading {
            color: #6b7280;
        }
        pre {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
        .test-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
        }
        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 CryptoBoost - Tests Complets</h1>
        <p>Suite de tests automatisés pour vérifier toutes les fonctionnalités de l'application CryptoBoost.</p>
        
        <div class="test-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalTests">0</div>
                <div class="stat-label">Tests Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="passedTests">0</div>
                <div class="stat-label">Réussis</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="failedTests">0</div>
                <div class="stat-label">Échoués</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successRate">0%</div>
                <div class="stat-label">Taux de Réussite</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        
        <div class="test-controls">
            <button onclick="runAllTests()">🚀 Exécuter Tous les Tests</button>
            <button onclick="runDatabaseTests()">🗄️ Tests Base de Données</button>
            <button onclick="runAPITests()">🔌 Tests API</button>
            <button onclick="runAuthTests()">🔐 Tests Authentification</button>
            <button onclick="runUITests()">🎨 Tests Interface</button>
            <button onclick="clearResults()">🧹 Effacer</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0
        };

        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info', section = 'general') {
            const sectionDiv = getOrCreateSection(section);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            sectionDiv.appendChild(div);
            
            // Update stats
            if (type === 'success') {
                testStats.passed++;
            } else if (type === 'error') {
                testStats.failed++;
            }
            testStats.total++;
            updateStats();
        }

        function getOrCreateSection(sectionName) {
            let section = document.getElementById(`section-${sectionName}`);
            if (!section) {
                section = document.createElement('div');
                section.id = `section-${sectionName}`;
                section.className = 'test-section';
                section.innerHTML = `<h3>📋 ${sectionName.toUpperCase()}</h3>`;
                resultsDiv.appendChild(section);
            }
            return section;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            
            const successRate = testStats.total > 0 ? Math.round((testStats.passed / testStats.total) * 100) : 0;
            document.getElementById('successRate').textContent = `${successRate}%`;
            
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = `${successRate}%`;
            
            if (successRate >= 80) {
                progressFill.style.background = '#10b981'; // Green
            } else if (successRate >= 60) {
                progressFill.style.background = '#f59e0b'; // Yellow
            } else {
                progressFill.style.background = '#ef4444'; // Red
            }
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0 };
            updateStats();
        }

        // 1. DATABASE TESTS
        async function runDatabaseTests() {
            addResult('🔄 Démarrage des tests de base de données...', 'info', 'database');

            const tables = ['users', 'investment_plans', 'transactions', 'company_wallets', 'config'];
            
            for (const table of tables) {
                try {
                    const data = await supabaseAPI.get(table);
                    if (data && Array.isArray(data)) {
                        addResult(`✅ Table "${table}": ${data.length} enregistrements trouvés`, 'success', 'database');
                    } else {
                        addResult(`⚠️ Table "${table}": Aucune donnée trouvée`, 'warning', 'database');
                    }
                } catch (error) {
                    addResult(`❌ Table "${table}": Erreur - ${error.message}`, 'error', 'database');
                }
            }
        }

        // 2. API TESTS
        async function runAPITests() {
            addResult('🔄 Démarrage des tests API...', 'info', 'api');

            // Test connection
            try {
                const response = await fetch(`${supabaseConfig.url}/rest/v1/`, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`
                    }
                });
                if (response.ok) {
                    addResult('✅ Connexion API Supabase: OK', 'success', 'api');
                } else {
                    addResult(`❌ Connexion API: Erreur ${response.status}`, 'error', 'api');
                }
            } catch (error) {
                addResult(`❌ Connexion API: ${error.message}`, 'error', 'api');
            }

            // Test specific API methods
            try {
                const plans = await supabaseAPI.getInvestmentPlans();
                if (plans && plans.length > 0) {
                    addResult(`✅ API getInvestmentPlans(): ${plans.length} plans récupérés`, 'success', 'api');
                } else {
                    addResult('⚠️ API getInvestmentPlans(): Aucun plan trouvé', 'warning', 'api');
                }
            } catch (error) {
                addResult(`❌ API getInvestmentPlans(): ${error.message}`, 'error', 'api');
            }

            try {
                const wallets = await supabaseAPI.getCompanyWallets();
                if (wallets && wallets.length > 0) {
                    addResult(`✅ API getCompanyWallets(): ${wallets.length} portefeuilles récupérés`, 'success', 'api');
                } else {
                    addResult('⚠️ API getCompanyWallets(): Aucun portefeuille trouvé', 'warning', 'api');
                }
            } catch (error) {
                addResult(`❌ API getCompanyWallets(): ${error.message}`, 'error', 'api');
            }
        }
