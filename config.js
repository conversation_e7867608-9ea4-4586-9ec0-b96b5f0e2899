// Supabase API Configuration

const supabaseConfig = {
    url: 'https://misciubbwfasvyeoqwgq.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pc2NpdWJid2Zhc3Z5ZW9xd2dxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NzcwMzMsImV4cCI6MjA2ODU1MzAzM30.oYxUXt776PAijkt1ziMBXQDnOHj-Q9uUjpdJdOgFRaM',
    serviceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pc2NpdWJid2Zhc3Z5ZW9xd2dxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1Mjk3NzAzMywiZXhwIjoyMDY4NTUzMDMzfQ.A-_RQakZBIVDxsHFzOicU-6kRXBaIb__serujpSLTkY'
};

// Tables configuration
const tables = {
    users: 'users',
    transactions: 'transactions', 
    config: 'config',
    plans: 'investment_plans',
    wallets: 'company_wallets'
};

// API endpoints
const apiEndpoints = {
    users: `${supabaseConfig.url}/rest/v1/${tables.users}`,
    transactions: `${supabaseConfig.url}/rest/v1/${tables.transactions}`,
    config: `${supabaseConfig.url}/rest/v1/${tables.config}`,
    plans: `${supabaseConfig.url}/rest/v1/${tables.plans}`,
    wallets: `${supabaseConfig.url}/rest/v1/${tables.wallets}`
};

// Make objects available globally
window.supabaseConfig = supabaseConfig;
window.apiEndpoints = apiEndpoints;
window.tables = tables;

console.log('✅ Config objects loaded and made global');