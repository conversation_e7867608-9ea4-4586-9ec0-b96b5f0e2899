<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord - CryptoBoost</title>
    <link rel="stylesheet" href="assets/css/modern-style.css">
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="config.js"></script>
</head>
<body class="dashboard-body">
    <!-- Header -->
    <header class="dashboard-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>🚀 CryptoBoost</h1>
                </div>
                <nav class="dashboard-nav">
                    <a href="#overview" class="nav-link active">Vue d'ensemble</a>
                    <a href="#investments" class="nav-link">Investissements</a>
                    <a href="#transactions" class="nav-link">Transactions</a>
                    <a href="#profile" class="nav-link">Profil</a>
                </nav>
                <div class="user-menu">
                    <span id="userName">Utilisateur</span>
                    <button onclick="logout()" class="btn-logout">Déconnexion</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Overview Section -->
            <section id="overview" class="dashboard-section active">
                <div class="section-header">
                    <h2>📊 Vue d'Ensemble</h2>
                    <p>Bienvenue sur votre tableau de bord CryptoBoost</p>
                </div>

                <!-- Balance Cards -->
                <div class="balance-grid">
                    <div class="balance-card primary">
                        <div class="card-icon">💰</div>
                        <div class="card-content">
                            <h3 id="totalBalance">0.00 €</h3>
                            <p>Solde Total</p>
                        </div>
                        <div class="card-trend positive">
                            <span>+5.2%</span>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="card-icon">📈</div>
                        <div class="card-content">
                            <h3 id="totalProfit">0.00 €</h3>
                            <p>Profits Totaux</p>
                        </div>
                        <div class="card-trend positive">
                            <span>+12.8%</span>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="card-icon">🏦</div>
                        <div class="card-content">
                            <h3 id="activeInvestments">0</h3>
                            <p>Investissements Actifs</p>
                        </div>
                    </div>
                    
                    <div class="balance-card">
                        <div class="card-icon">⏱️</div>
                        <div class="card-content">
                            <h3 id="nextPayout">--</h3>
                            <p>Prochain Paiement</p>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="charts-row">
                    <div class="chart-container">
                        <h3>📈 Évolution des Profits</h3>
                        <canvas id="profitChart"></canvas>
                    </div>
                    
                    <div class="chart-container">
                        <h3>🥧 Répartition des Investissements</h3>
                        <canvas id="portfolioChart"></canvas>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>Actions Rapides</h3>
                    <div class="actions-grid">
                        <button onclick="showDepositModal()" class="action-btn deposit">
                            <span class="btn-icon">💳</span>
                            <span class="btn-text">Déposer</span>
                        </button>
                        <button onclick="showWithdrawModal()" class="action-btn withdraw">
                            <span class="btn-icon">🏦</span>
                            <span class="btn-text">Retirer</span>
                        </button>
                        <button onclick="showInvestModal()" class="action-btn invest">
                            <span class="btn-icon">📊</span>
                            <span class="btn-text">Investir</span>
                        </button>
                        <button onclick="showSection('profile')" class="action-btn profile">
                            <span class="btn-icon">👤</span>
                            <span class="btn-text">Profil</span>
                        </button>
                    </div>
                </div>
            </section>

            <!-- Investments Section -->
            <section id="investments" class="dashboard-section">
                <div class="section-header">
                    <h2>📊 Mes Investissements</h2>
                    <button onclick="showInvestModal()" class="btn-primary">➕ Nouvel Investissement</button>
                </div>

                <!-- Investment Plans -->
                <div class="plans-grid" id="availablePlans">
                    <div class="loading">Chargement des plans...</div>
                </div>

                <!-- Active Investments -->
                <div class="investments-panel">
                    <h3>💼 Investissements Actifs</h3>
                    <div id="activeInvestmentsList" class="investments-list">
                        <div class="loading">Chargement...</div>
                    </div>
                </div>
            </section>

            <!-- Transactions Section -->
            <section id="transactions" class="dashboard-section">
                <div class="section-header">
                    <h2>💳 Historique des Transactions</h2>
                    <div class="filter-controls">
                        <select id="transactionFilter">
                            <option value="all">Toutes</option>
                            <option value="deposit">Dépôts</option>
                            <option value="withdrawal">Retraits</option>
                            <option value="profit">Profits</option>
                        </select>
                        <button onclick="refreshTransactions()" class="btn-refresh">🔄</button>
                    </div>
                </div>

                <div class="transactions-panel">
                    <div class="table-container">
                        <table id="transactionsTable" class="transactions-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Détails</th>
                                </tr>
                            </thead>
                            <tbody id="transactionsTableBody">
                                <tr><td colspan="5" class="loading">Chargement des transactions...</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section id="profile" class="dashboard-section">
                <div class="section-header">
                    <h2>👤 Mon Profil</h2>
                </div>

                <div class="profile-grid">
                    <div class="profile-card">
                        <h3>📋 Informations Personnelles</h3>
                        <form id="profileForm" class="profile-form">
                            <div class="form-group">
                                <label for="fullName">Nom Complet</label>
                                <input type="text" id="fullName" name="full_name" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Téléphone</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                            
                            <div class="form-group">
                                <label for="country">Pays</label>
                                <select id="country" name="country">
                                    <option value="FR">France</option>
                                    <option value="BE">Belgique</option>
                                    <option value="CH">Suisse</option>
                                    <option value="CA">Canada</option>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn-primary">💾 Sauvegarder</button>
                        </form>
                    </div>

                    <div class="profile-card">
                        <h3>🔒 Sécurité</h3>
                        <form id="securityForm" class="profile-form">
                            <div class="form-group">
                                <label for="currentPassword">Mot de passe actuel</label>
                                <input type="password" id="currentPassword" name="current_password">
                            </div>
                            
                            <div class="form-group">
                                <label for="newPassword">Nouveau mot de passe</label>
                                <input type="password" id="newPassword" name="new_password">
                            </div>
                            
                            <div class="form-group">
                                <label for="confirmPassword">Confirmer le mot de passe</label>
                                <input type="password" id="confirmPassword" name="confirm_password">
                            </div>
                            
                            <button type="submit" class="btn-primary">🔐 Changer le mot de passe</button>
                        </form>
                    </div>

                    <div class="profile-card">
                        <h3>📊 Statistiques</h3>
                        <div class="stats-list">
                            <div class="stat-item">
                                <span class="stat-label">Membre depuis</span>
                                <span class="stat-value" id="memberSince">--</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total investi</span>
                                <span class="stat-value" id="totalInvested">0.00 €</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">Total des profits</span>
                                <span class="stat-value" id="totalProfits">0.00 €</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">ROI moyen</span>
                                <span class="stat-value" id="averageROI">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Modals -->
    <div id="depositModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>💳 Effectuer un Dépôt</h3>
                <span class="close" onclick="closeModal('depositModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="depositForm">
                    <div class="form-group">
                        <label for="depositAmount">Montant (€)</label>
                        <input type="number" id="depositAmount" min="100" max="100000" required>
                        <small>Minimum: 100€ - Maximum: 100,000€</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="paymentMethod">Méthode de paiement</label>
                        <select id="paymentMethod" required>
                            <option value="">Sélectionner...</option>
                            <option value="card">Carte bancaire</option>
                            <option value="crypto">Cryptomonnaie</option>
                            <option value="bank">Virement bancaire</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn-primary full-width">💰 Déposer</button>
                </form>
            </div>
        </div>
    </div>

    <div id="withdrawModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🏦 Effectuer un Retrait</h3>
                <span class="close" onclick="closeModal('withdrawModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="withdrawForm">
                    <div class="form-group">
                        <label for="withdrawAmount">Montant (€)</label>
                        <input type="number" id="withdrawAmount" min="50" required>
                        <small>Solde disponible: <span id="availableBalance">0.00 €</span></small>
                    </div>
                    
                    <div class="form-group">
                        <label for="withdrawMethod">Méthode de retrait</label>
                        <select id="withdrawMethod" required>
                            <option value="">Sélectionner...</option>
                            <option value="bank">Virement bancaire</option>
                            <option value="crypto">Cryptomonnaie</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="withdrawAddress">Adresse/IBAN</label>
                        <input type="text" id="withdrawAddress" required>
                    </div>
                    
                    <button type="submit" class="btn-primary full-width">💸 Retirer</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/client.js"></script>
    <script src="assets/js/charts.js"></script>
    <script>
        // Initialisation du dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            loadUserData();
            loadDashboardData();
            initializeCharts();
        });

        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = this.getAttribute('href').substring(1);
                showSection(target);
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            });
        });

        function showSection(sectionId) {
            document.querySelectorAll('.dashboard-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(sectionId).classList.add('active');
        }

        function showDepositModal() {
            document.getElementById('depositModal').style.display = 'block';
        }

        function showWithdrawModal() {
            document.getElementById('withdrawModal').style.display = 'block';
            updateAvailableBalance();
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function logout() {
            localStorage.removeItem('userToken');
            window.location.href = 'login.html';
        }

        // Close modals when clicking outside
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
