<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i data-lucide="dollar-sign"></i>
        </div>
        <div class="stat-info">
            <h4>Total Investi</h4>
            <p id="total-invested">$0.00</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i data-lucide="trending-up"></i>
        </div>
        <div class="stat-info">
            <h4>Profit Total</h4>
            <p id="total-profit">$0.00</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i data-lucide="bot"></i>
        </div>
        <div class="stat-info">
            <h4>Bots Actifs</h4>
            <p id="active-bots">0</p>
        </div>
    </div>
</div>

<div class="content-card">
    <h3>Activité Récente</h3>
    <div class="table-responsive">
        <table class="activity-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Plan</th>
                    <th>Montant</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody id="recent-activity-body">
                <!-- Les lignes d'activité seront injectées ici par client.js -->
            </tbody>
        </table>
    </div>
</div>

