<div class="content-card">
    <div class="tabs">
        <button class="tab-link active" onclick="openTab(event, 'wallets')"><i data-lucide="wallet"></i> Mes Portefeuilles</button>
        <button class="tab-link" onclick="openTab(event, 'deposit')"><i data-lucide="arrow-down-circle"></i> <PERSON><PERSON><PERSON><PERSON>t</button>
        <button class="tab-link" onclick="openTab(event, 'withdraw')"><i data-lucide="arrow-up-circle"></i> Retrait</button>
    </div>

    <div id="wallets" class="tab-content" style="display: block;">
        <h3>Mes Soldes</h3>
        <div id="wallet-container" class="wallet-grid">
            <!-- Les cartes de portefeuille seront injectées ici -->
        </div>
    </div>

    <div id="deposit" class="tab-content">
        <h3>Déposer des Cryptos</h3>
        <form id="deposit-form" class="styled-form">
            <div class="form-group">
                <label for="deposit-currency">Choisissez une cryptomonnaie</label>
                <select id="deposit-currency" name="deposit-currency">
                    <!-- Options injectées par JS -->
                </select>
            </div>
            <div id="deposit-info" class="hidden">
                <p>Envoyez vos fonds à l'adresse ci-dessous :</p>
                <div class="wallet-address-box">
                    <strong id="deposit-address"></strong>
                    <div id="deposit-qr-code" class="qr-code"></div>
                </div>
            </div>
        </form>
    </div>

    <div id="withdraw" class="tab-content">
        <h3>Retirer des Cryptos</h3>
        <form id="withdraw-form" class="styled-form">
            <div class="form-group">
                <label for="withdraw-currency">Choisissez une cryptomonnaie</label>
                <select id="withdraw-currency" name="withdraw-currency">
                     <!-- Options injectées par JS -->
                </select>
            </div>
            <div class="form-group">
                <label for="withdraw-amount">Montant</label>
                <input type="number" id="withdraw-amount" name="withdraw-amount" placeholder="0.00" required>
            </div>
            <div class="form-group">
                <label for="withdraw-address">Votre adresse de réception</label>
                <input type="text" id="withdraw-address" name="withdraw-address" placeholder="Entrez votre adresse" required>
            </div>
            <button type="submit" class="btn btn-primary">Demander le Retrait</button>
        </form>
    </div>
</div>

<script>
// Helper function to manage tabs, as it's self-contained in this partial
function openTab(evt, tabName) {
    var i, tabcontent, tablinks;
    tabcontent = document.getElementsByClassName("tab-content");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
    tablinks = document.getElementsByClassName("tab-link");
    for (i = 0; i < tablinks.length; i++) {
        tablinks[i].className = tablinks[i].className.replace(" active", "");
    }
    document.getElementById(tabName).style.display = "block";
    evt.currentTarget.className += " active";
}
// Ensure Lucide icons are rendered when this partial is loaded
if (window.lucide) {
    window.lucide.createIcons();
}
</script>
