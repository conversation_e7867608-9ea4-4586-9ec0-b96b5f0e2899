<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Debug Scripts</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 11px;
            max-height: 150px;
        }
        .step {
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Scripts - Diagnostic Complet</h1>
        <p>Diagnostic étape par étape du chargement des scripts.</p>
        
        <button onclick="runDiagnostic()">🧪 Diagnostic Complet</button>
        <button onclick="testManualLoad()">📥 Test Chargement Manuel</button>
        <button onclick="clearResults()">🧹 Effacer</button>
        
        <div id="results"></div>
    </div>

    <script>
        console.log('🎯 Script de debug chargé');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function runDiagnostic() {
            clearResults();
            addResult('🔍 Diagnostic complet du chargement des scripts...', 'info');
            
            // Étape 1: Vérifier l'état initial
            addResult('<div class="step"><h4>Étape 1: État Initial</h4></div>', 'info');
            
            addResult(`Document ready state: ${document.readyState}`, 'info');
            addResult(`Window loaded: ${document.readyState === 'complete'}`, 'info');
            
            // Étape 2: Vérifier les variables globales de base
            addResult('<div class="step"><h4>Étape 2: Variables Globales</h4></div>', 'info');
            
            const globalVars = ['supabaseConfig', 'apiEndpoints', 'tables', 'supabaseAPI'];
            globalVars.forEach(varName => {
                const exists = typeof window[varName] !== 'undefined';
                const type = typeof window[varName];
                addResult(`${varName}: ${exists ? '✅' : '❌'} (type: ${type})`, exists ? 'success' : 'error');
                
                if (exists && window[varName]) {
                    try {
                        const preview = JSON.stringify(window[varName], null, 2).substring(0, 200);
                        addResult(`<pre>${preview}${preview.length >= 200 ? '...' : ''}</pre>`, 'info');
                    } catch (e) {
                        addResult(`Contenu: ${window[varName].toString().substring(0, 100)}...`, 'info');
                    }
                }
            });
            
            // Étape 3: Vérifier les erreurs JavaScript
            addResult('<div class="step"><h4>Étape 3: Erreurs JavaScript</h4></div>', 'info');
            
            // Capturer les erreurs
            window.addEventListener('error', (e) => {
                addResult(`❌ Erreur JS: ${e.message} (${e.filename}:${e.lineno})`, 'error');
            });
            
            // Étape 4: Test de chargement manuel
            addResult('<div class="step"><h4>Étape 4: Test Chargement Manuel</h4></div>', 'info');
            testManualLoad();
        }

        async function testManualLoad() {
            addResult('📥 Test de chargement manuel des scripts...', 'info');
            
            try {
                // Test 1: Charger config.js manuellement
                addResult('Chargement de config.js...', 'info');
                const configResponse = await fetch('config.js');
                const configText = await configResponse.text();
                
                if (configResponse.ok) {
                    addResult('✅ config.js récupéré avec succès', 'success');
                    addResult(`<pre>${configText.substring(0, 300)}...</pre>`, 'info');
                    
                    // Exécuter le script
                    try {
                        eval(configText);
                        addResult('✅ config.js exécuté', 'success');
                        
                        // Vérifier si les variables sont maintenant disponibles
                        if (typeof supabaseConfig !== 'undefined') {
                            addResult('✅ supabaseConfig maintenant disponible', 'success');
                            window.supabaseConfig = supabaseConfig;
                        }
                        if (typeof apiEndpoints !== 'undefined') {
                            addResult('✅ apiEndpoints maintenant disponible', 'success');
                            window.apiEndpoints = apiEndpoints;
                        }
                    } catch (evalError) {
                        addResult(`❌ Erreur lors de l'exécution de config.js: ${evalError.message}`, 'error');
                    }
                } else {
                    addResult(`❌ Impossible de charger config.js: ${configResponse.status}`, 'error');
                }
                
                // Test 2: Charger supabase-api.js manuellement
                addResult('Chargement de supabase-api.js...', 'info');
                const apiResponse = await fetch('assets/js/supabase-api.js');
                const apiText = await apiResponse.text();
                
                if (apiResponse.ok) {
                    addResult('✅ supabase-api.js récupéré avec succès', 'success');
                    
                    // Exécuter le script
                    try {
                        eval(apiText);
                        addResult('✅ supabase-api.js exécuté', 'success');
                        
                        // Vérifier si supabaseAPI est maintenant disponible
                        if (typeof supabaseAPI !== 'undefined') {
                            addResult('✅ supabaseAPI maintenant disponible', 'success');
                            window.supabaseAPI = supabaseAPI;
                        }
                    } catch (evalError) {
                        addResult(`❌ Erreur lors de l'exécution de supabase-api.js: ${evalError.message}`, 'error');
                    }
                } else {
                    addResult(`❌ Impossible de charger supabase-api.js: ${apiResponse.status}`, 'error');
                }
                
                // Test final
                addResult('<div class="step"><h4>Test Final</h4></div>', 'info');
                if (window.supabaseAPI && window.supabaseConfig) {
                    addResult('🎉 Tous les objets sont maintenant disponibles !', 'success');
                    
                    // Test d'un appel API
                    try {
                        const users = await window.supabaseAPI.get('users');
                        addResult(`✅ Test API réussi: ${users ? users.length : 0} utilisateurs`, 'success');
                    } catch (apiError) {
                        addResult(`⚠️ Objets disponibles mais erreur API: ${apiError.message}`, 'warning');
                    }
                } else {
                    addResult('❌ Les objets ne sont toujours pas disponibles après chargement manuel', 'error');
                }
                
            } catch (error) {
                addResult(`❌ Erreur lors du test de chargement manuel: ${error.message}`, 'error');
            }
        }

        // Diagnostic automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                addResult('🎯 Page chargée. Cliquez sur "Diagnostic Complet" pour analyser.', 'info');
            }, 100);
        });
    </script>
</body>
</html>
