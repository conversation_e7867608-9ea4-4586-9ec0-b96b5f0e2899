<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Transactions - CryptoBoost</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .debug-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover { background: #4338ca; }
        pre {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .url-display {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔍 Debug Transactions - CryptoBoost</h1>
        <p>Diagnostic détaillé du problème de chargement des transactions.</p>
        
        <button onclick="testDirectAPI()">🔌 Test API Direct</button>
        <button onclick="testWithFilters()">🔍 Test avec Filtres</button>
        <button onclick="testAllTransactions()">📋 Toutes les Transactions</button>
        <button onclick="testSpecificUser()">👤 Utilisateur Spécifique</button>
        <button onclick="clearResults()">🧹 Effacer</button>

        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testDirectAPI() {
            clearResults();
            addResult('🔌 Test de l\'API directe...', 'info');

            try {
                // Test direct avec fetch
                const url = `${supabaseConfig.url}/rest/v1/transactions`;
                addResult(`<div class="url-display">URL: ${url}</div>`, 'info');

                const response = await fetch(url, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                addResult(`Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Données reçues: ${data.length} transactions`, 'success');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Erreur: ${errorText}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Erreur de requête: ${error.message}`, 'error');
            }
        }

        async function testWithFilters() {
            clearResults();
            addResult('🔍 Test avec filtres...', 'info');

            try {
                // Test avec filtre utilisateur
                const userEmail = '<EMAIL>';
                const url = `${supabaseConfig.url}/rest/v1/transactions?user_email=eq.${encodeURIComponent(userEmail)}`;
                addResult(`<div class="url-display">URL avec filtre: ${url}</div>`, 'info');

                const response = await fetch(url, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                addResult(`Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Transactions pour ${userEmail}: ${data.length}`, 'success');
                    if (data.length > 0) {
                        addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                    }
                } else {
                    const errorText = await response.text();
                    addResult(`❌ Erreur: ${errorText}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Erreur de requête: ${error.message}`, 'error');
            }
        }

        async function testAllTransactions() {
            clearResults();
            addResult('📋 Test de toutes les transactions...', 'info');

            try {
                // Test via supabaseAPI
                addResult('Test via supabaseAPI.get("transactions")...', 'info');
                const startTime = Date.now();
                
                const data = await supabaseAPI.get('transactions');
                const endTime = Date.now();
                
                addResult(`⏱️ Temps de réponse: ${endTime - startTime}ms`, 'info');
                
                if (data && Array.isArray(data)) {
                    addResult(`✅ Transactions récupérées: ${data.length}`, 'success');
                    data.forEach((tx, index) => {
                        addResult(`${index + 1}. ${tx.type} - ${tx.amount}€ - ${tx.user_email} - ${tx.status}`, 'info');
                    });
                } else {
                    addResult(`⚠️ Données reçues: ${typeof data}`, 'warning');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                }

            } catch (error) {
                addResult(`❌ Erreur supabaseAPI: ${error.message}`, 'error');
                addResult(`Stack: ${error.stack}`, 'error');
            }
        }

        async function testSpecificUser() {
            clearResults();
            addResult('👤 Test utilisateur spécifique...', 'info');

            try {
                const userEmail = '<EMAIL>';
                addResult(`Test pour: ${userEmail}`, 'info');
                
                const startTime = Date.now();
                const data = await supabaseAPI.getUserTransactions(userEmail);
                const endTime = Date.now();
                
                addResult(`⏱️ Temps de réponse: ${endTime - startTime}ms`, 'info');
                
                if (data && Array.isArray(data)) {
                    addResult(`✅ Transactions utilisateur: ${data.length}`, 'success');
                    data.forEach((tx, index) => {
                        addResult(`${index + 1}. ${tx.type} - ${tx.amount}€ - ${tx.status} - ${tx.created_at}`, 'info');
                    });
                } else {
                    addResult(`⚠️ Données reçues: ${typeof data}`, 'warning');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                }

                // Test avec un autre utilisateur
                addResult('<NAME_EMAIL>...', 'info');
                const demoData = await supabaseAPI.getUserTransactions('<EMAIL>');
                if (demoData && Array.isArray(demoData)) {
                    addResult(`✅ Transactions demo: ${demoData.length}`, 'success');
                } else {
                    addResult(`⚠️ Pas de données pour demo`, 'warning');
                }

            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
                addResult(`Stack: ${error.stack}`, 'error');
            }
        }

        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🔍 Page de debug chargée. Cliquez sur un bouton pour diagnostiquer.', 'info');
            
            // Vérifier la configuration
            if (typeof supabaseConfig !== 'undefined') {
                addResult(`✅ Configuration Supabase chargée: ${supabaseConfig.url}`, 'success');
            } else {
                addResult('❌ Configuration Supabase manquante', 'error');
            }
            
            if (typeof supabaseAPI !== 'undefined') {
                addResult('✅ API Supabase chargée', 'success');
            } else {
                addResult('❌ API Supabase manquante', 'error');
            }
        });
    </script>
</body>
</html>
