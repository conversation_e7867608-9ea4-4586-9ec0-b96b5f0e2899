<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnostic Fonctions Client</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .function-test {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .available { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .missing { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .function-name { font-weight: bold; }
        .function-status { font-size: 18px; }
        .summary {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .critical { background: #fef2f2; border-color: #ef4444; }
        .good { background: #f0fdf4; border-color: #22c55e; }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover { background: #4338ca; }
        .script-status {
            background: #f3f4f6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic des Fonctions Client</h1>
        <p>Analyse détaillée des fonctions JavaScript disponibles dans l'application CryptoBoost.</p>
        
        <button onclick="runDiagnostic()">🧪 Lancer Diagnostic</button>
        <button onclick="testScriptLoading()">📜 Test Chargement Scripts</button>
        <button onclick="clearResults()">🧹 Effacer</button>

        <div id="scriptStatus" class="script-status"></div>
        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script src="assets/js/client.js"></script>
    <script>
        // Liste des fonctions critiques à vérifier
        const criticalFunctions = [
            { name: 'loadPublicPlans', description: 'Chargement des plans publics', critical: true },
            { name: 'navigateTo', description: 'Navigation entre pages', critical: true },
            { name: 'renderAppLayout', description: 'Rendu de l\'interface', critical: true },
            { name: 'loadHomePage', description: 'Chargement page d\'accueil', critical: true },
            { name: 'loadPlansPage', description: 'Chargement page plans', critical: true },
            { name: 'loadHistoryPage', description: 'Chargement historique', critical: false },
            { name: 'loadProfilePage', description: 'Chargement profil', critical: false },
            { name: 'loadWalletPage', description: 'Chargement portefeuille', critical: false },
            { name: 'loadExchangePage', description: 'Chargement exchange', critical: false },
            { name: 'invest', description: 'Fonction d\'investissement', critical: true },
            { name: 'logoutUser', description: 'Déconnexion utilisateur', critical: true },
            { name: 'updateExchangeRate', description: 'Mise à jour taux change', critical: false }
        ];

        const apiObjects = [
            { name: 'supabaseAPI', description: 'API Supabase', critical: true },
            { name: 'supabaseConfig', description: 'Configuration Supabase', critical: true }
        ];

        function addResult(content) {
            document.getElementById('results').appendChild(content);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('scriptStatus').innerHTML = '';
        }

        function createFunctionTest(func, isAvailable) {
            const div = document.createElement('div');
            div.className = `function-test ${isAvailable ? 'available' : 'missing'}`;
            
            const criticalBadge = func.critical ? ' 🔴 CRITIQUE' : ' 🟡 OPTIONNEL';
            
            div.innerHTML = `
                <div>
                    <span class="function-name">${func.name}()</span>${criticalBadge}<br>
                    <small>${func.description}</small>
                </div>
                <div class="function-status">${isAvailable ? '✅' : '❌'}</div>
            `;
            return div;
        }

        function testScriptLoading() {
            const statusDiv = document.getElementById('scriptStatus');
            statusDiv.innerHTML = '<h3>📜 État du Chargement des Scripts</h3>';

            // Test config.js
            if (typeof supabaseConfig !== 'undefined') {
                statusDiv.innerHTML += '<div style="color: green;">✅ config.js: Chargé</div>';
            } else {
                statusDiv.innerHTML += '<div style="color: red;">❌ config.js: Non chargé</div>';
            }

            // Test supabase-api.js
            if (typeof supabaseAPI !== 'undefined') {
                statusDiv.innerHTML += '<div style="color: green;">✅ supabase-api.js: Chargé</div>';
            } else {
                statusDiv.innerHTML += '<div style="color: red;">❌ supabase-api.js: Non chargé</div>';
            }

            // Test client.js
            if (typeof loadPublicPlans !== 'undefined') {
                statusDiv.innerHTML += '<div style="color: green;">✅ client.js: Chargé</div>';
            } else {
                statusDiv.innerHTML += '<div style="color: red;">❌ client.js: Non chargé ou fonctions non disponibles</div>';
            }

            // Test des objets globaux
            statusDiv.innerHTML += '<br><h4>🌐 Objets Globaux:</h4>';
            apiObjects.forEach(obj => {
                const isAvailable = typeof window[obj.name] !== 'undefined';
                const status = isAvailable ? '✅' : '❌';
                const color = isAvailable ? 'green' : 'red';
                statusDiv.innerHTML += `<div style="color: ${color};">${status} ${obj.name}: ${obj.description}</div>`;
            });
        }

        function runDiagnostic() {
            clearResults();
            
            let availableCount = 0;
            let criticalMissing = 0;
            let totalCritical = 0;

            // Test chaque fonction
            criticalFunctions.forEach(func => {
                const isAvailable = typeof window[func.name] === 'function';
                const testDiv = createFunctionTest(func, isAvailable);
                addResult(testDiv);

                if (isAvailable) {
                    availableCount++;
                } else if (func.critical) {
                    criticalMissing++;
                }

                if (func.critical) {
                    totalCritical++;
                }
            });

            // Résumé
            const summaryDiv = document.createElement('div');
            const criticalOk = criticalMissing === 0;
            summaryDiv.className = `summary ${criticalOk ? 'good' : 'critical'}`;
            
            const percentage = Math.round((availableCount / criticalFunctions.length) * 100);
            
            summaryDiv.innerHTML = `
                <h3>${criticalOk ? '✅' : '❌'} Résumé du Diagnostic</h3>
                <p><strong>Fonctions disponibles:</strong> ${availableCount}/${criticalFunctions.length} (${percentage}%)</p>
                <p><strong>Fonctions critiques manquantes:</strong> ${criticalMissing}/${totalCritical}</p>
                <p><strong>État:</strong> ${criticalOk ? 
                    '🎉 Toutes les fonctions critiques sont disponibles !' : 
                    `⚠️ ${criticalMissing} fonction(s) critique(s) manquante(s)`
                }</p>
                ${!criticalOk ? '<p><strong>Action requise:</strong> Vérifier le chargement du script client.js</p>' : ''}
            `;
            
            addResult(summaryDiv);

            // Test automatique du chargement des scripts
            testScriptLoading();
        }

        // Diagnostic automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                runDiagnostic();
            }, 500);
        });
    </script>
</body>
</html>
