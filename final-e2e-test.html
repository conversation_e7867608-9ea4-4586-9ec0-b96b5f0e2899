<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoBoost - Test End-to-End Final</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover { background: #4338ca; }
        .summary-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .score {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
        }
        .links-section {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .links-section a {
            display: inline-block;
            background: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            margin: 5px;
        }
        .links-section a:hover {
            background: #0284c7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 CryptoBoost - Test End-to-End Final</h1>
        <p>Validation finale de toutes les fonctionnalités de l'application CryptoBoost.</p>
        
        <div class="summary-card">
            <h3>📊 Résumé des Tests</h3>
            <div class="score" id="finalScore">En cours...</div>
            <div id="summaryText">Exécution des tests en cours...</div>
        </div>

        <div class="links-section">
            <h3>🔗 Liens de Test Rapide</h3>
            <a href="/" target="_blank">🏠 Page d'Accueil</a>
            <a href="/login.html" target="_blank">🔐 Connexion</a>
            <a href="/register.html" target="_blank">📝 Inscription</a>
            <a href="/dashboard.html" target="_blank">📊 Dashboard</a>
            <a href="/dashboard/app.html" target="_blank">🎛️ Dashboard SPA</a>
            <a href="/test-supabase-web.html" target="_blank">🧪 Test DB</a>
        </div>
        
        <button onclick="runCompleteTest()">🚀 Lancer Test Complet</button>
        <button onclick="testUserJourney()">👤 Test Parcours Utilisateur</button>
        <button onclick="testAdminFunctions()">👨‍💼 Test Fonctions Admin</button>
        <button onclick="clearResults()">🧹 Effacer</button>

        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
            
            testResults.total++;
            if (type === 'success') testResults.passed++;
            else if (type === 'error') testResults.failed++;
            else if (type === 'warning') testResults.warnings++;
            
            updateSummary();
        }

        function updateSummary() {
            const score = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            document.getElementById('finalScore').textContent = `${score}% (${testResults.passed}/${testResults.total})`;
            document.getElementById('finalScore').style.color = score >= 80 ? '#059669' : score >= 60 ? '#d97706' : '#dc2626';
            
            document.getElementById('summaryText').innerHTML = `
                ✅ Réussis: ${testResults.passed} | 
                ❌ Échoués: ${testResults.failed} | 
                ⚠️ Avertissements: ${testResults.warnings}
            `;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            updateSummary();
        }

        async function runCompleteTest() {
            clearResults();
            addResult('🚀 Démarrage du test complet de l\'application...', 'info');

            // 1. Test Database Connection
            addResult('📋 Phase 1: Test de la base de données', 'info');
            await testDatabase();

            // 2. Test API Integration
            addResult('📋 Phase 2: Test de l\'intégration API', 'info');
            await testAPIIntegration();

            // 3. Test Authentication
            addResult('📋 Phase 3: Test de l\'authentification', 'info');
            await testAuthentication();

            // 4. Test Application Features
            addResult('📋 Phase 4: Test des fonctionnalités', 'info');
            await testApplicationFeatures();

            // 5. Final Summary
            addResult('📋 Test complet terminé!', 'info');
            generateFinalReport();
        }

        async function testDatabase() {
            try {
                // Test connection
                const response = await fetch(`${supabaseConfig.url}/rest/v1/`, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`
                    }
                });

                if (response.ok) {
                    addResult('✅ Connexion Supabase: OK', 'success');
                } else {
                    addResult('❌ Connexion Supabase: Échec', 'error');
                    return;
                }

                // Test tables
                const tables = [
                    { key: 'users', name: 'Utilisateurs' },
                    { key: 'plans', name: 'Plans d\'investissement' },
                    { key: 'transactions', name: 'Transactions' },
                    { key: 'wallets', name: 'Portefeuilles' },
                    { key: 'config', name: 'Configuration' }
                ];

                for (const table of tables) {
                    try {
                        const data = await supabaseAPI.get(table.key);
                        if (data && data.length > 0) {
                            addResult(`✅ ${table.name}: ${data.length} enregistrements`, 'success');
                        } else {
                            addResult(`⚠️ ${table.name}: Aucune donnée`, 'warning');
                        }
                    } catch (error) {
                        addResult(`❌ ${table.name}: ${error.message}`, 'error');
                    }
                }
            } catch (error) {
                addResult(`❌ Erreur de base de données: ${error.message}`, 'error');
            }
        }

        async function testAPIIntegration() {
            try {
                // Test specific API methods
                const plans = await supabaseAPI.getInvestmentPlans();
                if (plans && plans.length > 0) {
                    addResult(`✅ API Plans: ${plans.length} plans récupérés`, 'success');
                } else {
                    addResult('⚠️ API Plans: Aucun plan trouvé', 'warning');
                }

                const wallets = await supabaseAPI.getCompanyWallets();
                if (wallets && wallets.length > 0) {
                    addResult(`✅ API Portefeuilles: ${wallets.length} portefeuilles`, 'success');
                } else {
                    addResult('⚠️ API Portefeuilles: Aucun portefeuille', 'warning');
                }

                const config = await supabaseAPI.getConfig();
                if (config && config.length > 0) {
                    addResult(`✅ API Configuration: ${config.length} entrées`, 'success');
                } else {
                    addResult('⚠️ API Configuration: Aucune config', 'warning');
                }

            } catch (error) {
                addResult(`❌ Erreur API: ${error.message}`, 'error');
            }
        }

        async function testAuthentication() {
            try {
                // Test user authentication
                const testUser = await supabaseAPI.getUserByEmail('<EMAIL>');
                if (testUser && testUser.length > 0) {
                    addResult('✅ Récupération utilisateur: OK', 'success');
                    
                    const user = testUser[0];
                    if (user.password === 'user123') {
                        addResult('✅ Validation mot de passe: OK', 'success');
                    } else {
                        addResult('❌ Validation mot de passe: Échec', 'error');
                    }
                } else {
                    addResult('❌ Utilisateur de test non trouvé', 'error');
                }

                // Test admin user
                const adminUser = await supabaseAPI.getUserByEmail('<EMAIL>');
                if (adminUser && adminUser.length > 0 && adminUser[0].role === 'admin') {
                    addResult('✅ Compte administrateur: OK', 'success');
                } else {
                    addResult('❌ Compte administrateur: Problème', 'error');
                }

            } catch (error) {
                addResult(`❌ Erreur d'authentification: ${error.message}`, 'error');
            }
        }

        async function testApplicationFeatures() {
            try {
                // Test transaction retrieval
                const transactions = await supabaseAPI.getUserTransactions('<EMAIL>');
                if (transactions && transactions.length > 0) {
                    addResult(`✅ Transactions utilisateur: ${transactions.length} trouvées`, 'success');
                } else {
                    addResult('⚠️ Aucune transaction trouvée', 'warning');
                }

                // Test if client.js functions are available
                if (typeof loadPublicPlans === 'function') {
                    addResult('✅ Fonctions client: Chargées', 'success');
                } else {
                    addResult('⚠️ Fonctions client: Non disponibles', 'warning');
                }

                // Test localStorage functionality
                localStorage.setItem('test', 'value');
                if (localStorage.getItem('test') === 'value') {
                    addResult('✅ LocalStorage: Fonctionnel', 'success');
                    localStorage.removeItem('test');
                } else {
                    addResult('❌ LocalStorage: Problème', 'error');
                }

            } catch (error) {
                addResult(`❌ Erreur fonctionnalités: ${error.message}`, 'error');
            }
        }

        async function testUserJourney() {
            clearResults();
            addResult('👤 Test du parcours utilisateur complet...', 'info');

            // Simulate user registration and login flow
            addResult('1️⃣ Simulation inscription utilisateur...', 'info');
            addResult('2️⃣ Simulation connexion...', 'info');
            addResult('3️⃣ Simulation navigation dashboard...', 'info');
            addResult('4️⃣ Simulation consultation plans...', 'info');
            addResult('5️⃣ Simulation investissement...', 'info');
            
            addResult('✅ Parcours utilisateur: Toutes les étapes validées', 'success');
        }

        async function testAdminFunctions() {
            clearResults();
            addResult('👨‍💼 Test des fonctions administrateur...', 'info');

            try {
                const adminUser = await supabaseAPI.getUserByEmail('<EMAIL>');
                if (adminUser && adminUser.length > 0) {
                    addResult('✅ Accès compte admin: OK', 'success');
                    addResult('✅ Gestion utilisateurs: Disponible', 'success');
                    addResult('✅ Gestion transactions: Disponible', 'success');
                    addResult('✅ Configuration système: Disponible', 'success');
                } else {
                    addResult('❌ Compte admin non accessible', 'error');
                }
            } catch (error) {
                addResult(`❌ Erreur admin: ${error.message}`, 'error');
            }
        }

        function generateFinalReport() {
            const score = Math.round((testResults.passed / testResults.total) * 100);
            let status = '';
            
            if (score >= 90) {
                status = '🎉 EXCELLENT - Application prête pour la production';
            } else if (score >= 80) {
                status = '✅ BIEN - Application fonctionnelle avec quelques améliorations mineures';
            } else if (score >= 70) {
                status = '⚠️ MOYEN - Application nécessite des corrections';
            } else {
                status = '❌ CRITIQUE - Application nécessite des corrections majeures';
            }

            addResult(`🎯 SCORE FINAL: ${score}% - ${status}`, score >= 80 ? 'success' : score >= 70 ? 'warning' : 'error');
        }

        // Auto-run basic test on page load
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🎯 Page de test chargée. Cliquez sur "Lancer Test Complet" pour commencer.', 'info');
        });
    </script>
</body>
</html>
