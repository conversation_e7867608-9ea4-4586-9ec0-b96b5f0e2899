<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoBoost - Plateforme de Trading Futuriste</title>
    <meta name="description" content="CryptoBoost - La plateforme de trading crypto la plus avancée. Interface futuriste, IA intégrée, trading automatisé.">

    <!-- Fonts Futuristes -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700;800&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/cryptoboost-design-system.css">
    <link rel="stylesheet" href="assets/css/futuristic-landing.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body class="futuristic-landing">
    <!-- Cyber Grid Background -->
    <div class="cyber-grid"></div>

    <!-- Matrix Rain Effect -->
    <div class="matrix-rain" id="matrix-rain"></div>

    <!-- Navigation Futuriste -->
    <nav class="cyber-nav" id="cyber-nav">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-logo">
                    <i class="fas fa-rocket"></i>
                    <div class="logo-glow"></div>
                </div>
                <span class="brand-text">CryptoBoost</span>
                <div class="brand-subtitle">FUTURE TRADING</div>
            </div>

            <div class="nav-menu" id="nav-menu">
                <a href="#home" class="nav-link active" data-section="home">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="#features" class="nav-link" data-section="features">
                    <i class="fas fa-bolt"></i>
                    <span>Fonctionnalités</span>
                </a>
                <a href="#trading" class="nav-link" data-section="trading">
                    <i class="fas fa-chart-line"></i>
                    <span>Trading</span>
                </a>
                <a href="#ai" class="nav-link" data-section="ai">
                    <i class="fas fa-brain"></i>
                    <span>IA</span>
                </a>
                <a href="#plans" class="nav-link" data-section="plans">
                    <i class="fas fa-star"></i>
                    <span>Plans</span>
                </a>
                <a href="#contact" class="nav-link" data-section="contact">
                    <i class="fas fa-satellite-dish"></i>
                    <span>Contact</span>
                </a>
            </div>

            <div class="nav-actions">
                <a href="login-modern.html" class="btn btn-outline btn-sm">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Connexion</span>
                </a>
                <a href="register-modern.html" class="btn btn-primary btn-sm">
                    <i class="fas fa-rocket"></i>
                    <span>Commencer</span>
                </a>
                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section Futuriste -->
    <section class="hero-section" id="home">
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    <span>Plateforme #1 en France</span>
                    <div class="badge-glow"></div>
                </div>

                <h1 class="hero-title">
                    <span class="title-line">L'AVENIR DU</span>
                    <span class="title-highlight">TRADING CRYPTO</span>
                    <span class="title-line">EST ICI</span>
                </h1>

                <p class="hero-description">
                    Découvrez CryptoBoost, la plateforme de trading crypto la plus avancée.
                    Interface futuriste, IA intégrée, trading automatisé et rendements optimisés.
                </p>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-value" data-count="50000">0</div>
                        <div class="stat-label">Traders Actifs</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" data-count="2500000">0</div>
                        <div class="stat-label">€ Volume 24h</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" data-count="99">0</div>
                        <div class="stat-label">% Uptime</div>
                    </div>
                </div>

                <div class="hero-actions">
                    <a href="register-modern.html" class="btn btn-primary btn-xl">
                        <i class="fas fa-rocket"></i>
                        <span>Commencer Maintenant</span>
                        <div class="btn-particles"></div>
                    </a>
                    <a href="#demo" class="btn btn-outline btn-xl">
                        <i class="fas fa-play"></i>
                        <span>Voir la Démo</span>
                    </a>
                </div>
            </div>

            <div class="hero-visual">
                <div class="trading-interface">
                    <div class="interface-header">
                        <div class="interface-title">CRYPTOBOOST TERMINAL</div>
                        <div class="interface-status">
                            <div class="status-dot online"></div>
                            <span>ONLINE</span>
                        </div>
                    </div>

                    <div class="interface-content">
                        <div class="crypto-ticker">
                            <div class="ticker-item">
                                <span class="crypto-symbol">BTC</span>
                                <span class="crypto-price">€42,350</span>
                                <span class="crypto-change positive">+2.4%</span>
                            </div>
                            <div class="ticker-item">
                                <span class="crypto-symbol">ETH</span>
                                <span class="crypto-price">€2,890</span>
                                <span class="crypto-change negative">-1.2%</span>
                            </div>
                            <div class="ticker-item">
                                <span class="crypto-symbol">ADA</span>
                                <span class="crypto-price">€0.45</span>
                                <span class="crypto-change positive">+5.7%</span>
                            </div>
                        </div>

                        <div class="chart-container">
                            <canvas id="hero-chart"></canvas>
                        </div>

                        <div class="trading-controls">
                            <button class="control-btn active">BUY</button>
                            <button class="control-btn">SELL</button>
                            <button class="control-btn">AUTO</button>
                        </div>
                    </div>
                </div>

                <div class="floating-elements">
                    <div class="floating-crypto btc">
                        <i class="fab fa-bitcoin"></i>
                    </div>
                    <div class="floating-crypto eth">
                        <i class="fab fa-ethereum"></i>
                    </div>
                    <div class="floating-crypto">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="hero-particles" id="hero-particles"></div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script src="assets/js/futuristic-effects.js"></script>
    <script>
        // Initialize live data on homepage
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🏠 Homepage loading with live data...');

            try {
                // Wait for Supabase API
                let attempts = 0;
                while (attempts < 50 && (!window.supabaseAPI || !window.supabaseAPI.isReady())) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }

                if (window.supabaseAPI && window.supabaseAPI.isReady()) {
                    // Load live stats
                    await loadLiveStats();
                    console.log('✅ Live stats loaded');
                } else {
                    console.log('⚠️ Using fallback data for homepage');
                    loadFallbackStats();
                }

            } catch (error) {
                console.error('❌ Error loading live data:', error);
                loadFallbackStats();
            }
        });

        // Load live statistics from Supabase
        async function loadLiveStats() {
            try {
                const metrics = await window.supabaseAPI.getAdminMetrics();

                // Update online traders
                const onlineTraders = document.getElementById('online-traders');
                if (onlineTraders) {
                    onlineTraders.textContent = metrics.activeUsers.toLocaleString();
                }

                // Update daily volume
                const dailyVolume = document.getElementById('daily-volume');
                if (dailyVolume) {
                    dailyVolume.textContent = '€' + (metrics.dailyVolume / 1000000).toFixed(1) + 'M';
                }

                // Update total users
                const totalUsers = document.getElementById('total-users');
                if (totalUsers) {
                    totalUsers.textContent = metrics.totalUsers.toLocaleString();
                }

                console.log('📊 Live stats updated from Supabase');

            } catch (error) {
                console.error('❌ Error loading live stats:', error);
                throw error;
            }
        }

        // Fallback stats if Supabase is not available
        function loadFallbackStats() {
            const onlineTraders = document.getElementById('online-traders');
            if (onlineTraders) {
                onlineTraders.textContent = '45,892';
            }

            const dailyVolume = document.getElementById('daily-volume');
            if (dailyVolume) {
                dailyVolume.textContent = '€2.4M';
            }

            const totalUsers = document.getElementById('total-users');
            if (totalUsers) {
                totalUsers.textContent = '125,000';
            }

            console.log('📊 Fallback stats loaded');
        }
    </script>
</body>
</html>