<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - CryptoBoost</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Montserrat:wght@700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="assets/css/modern-style.css">
</head>
<body>
    <div class="background-glow"></div>

    <main class="auth-container">
        <div class="auth-card" data-aos="fade-up">
            <a href="index.html" class="logo">CryptoBoost</a>
            <h1>Bon retour parmi nous</h1>
            <p class="subtitle">Connectez-vous pour accéder à votre tableau de bord.</p>
            
            <form id="login-form">
                <div class="form-group">
                    <label for="email">Adresse e-mail</label>
                    <input type="email" id="email" class="form-control" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" class="form-control" required placeholder="••••••••">
                </div>
                <button type="submit" class="btn btn-primary">Se connecter</button>
            </form>
            
            <div class="auth-footer">
                <p><a href="#">Mot de passe oublié ?</a></p>
                <p>Pas encore de compte ? <a href="register.html">Inscrivez-vous</a></p>
            </div>
        </div>
    </main>

    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
      AOS.init({
          duration: 800,
          once: true,
      });
    </script>

    <!-- Scripts d'authentification -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="config.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/auth.js"></script>
    <script>
        document.getElementById('login-form').addEventListener('submit', loginUser);
    </script>
</body>
</html>
