{"name": "cryptoboost", "version": "2.0.0", "description": "Plateforme moderne d'investissement en cryptomonnaies avec Supabase", "main": "index.html", "scripts": {"start": "python -m http.server 8080", "serve": "npx serve .", "test": "powershell -ExecutionPolicy Bypass -File test-simple.ps1", "test-full": "powershell -ExecutionPolicy Bypass -File test-supabase-final-real.ps1", "setup": "echo 'Executez complete-setup.sql dans Supabase SQL Editor'", "build": "echo 'Projet statique - pas de build necessaire'", "deploy": "echo '<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ou GitHub Pages'"}, "keywords": ["crypto", "investment", "supabase", "javascript", "html", "css", "finance", "trading", "dashboard"], "author": "CryptoBoost Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/username/cryptoboost.git"}, "bugs": {"url": "https://github.com/username/cryptoboost/issues"}, "homepage": "https://cryptoboost-demo.netlify.app", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "devDependencies": {"serve": "^14.0.0"}, "dependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "config": {"supabase": {"url": "https://misciubbwfasvyeoqwgq.supabase.co", "configured": true}}, "files": ["index.html", "login.html", "register.html", "dashboard.html", "admin.html", "styles.css", "script.js", "config.js", "complete-setup.sql", "test-*.ps1", "test-*.html", "README-NOUVEAU.md", "INSTALLATION.md", "DEPLOIEMENT.md"]}