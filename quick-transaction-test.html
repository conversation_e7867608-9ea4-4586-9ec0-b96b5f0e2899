<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Rapide Transactions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Rapide Transactions</h1>
        
        <button onclick="testQuick()">⚡ Test Rapide</button>
        <button onclick="testDetailed()">🔍 Test Détaillé</button>
        <button onclick="clearResults()">🧹 Effacer</button>
        
        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testQuick() {
            clearResults();
            addResult('⚡ Test rapide en cours...', 'info');

            try {
                // Test 1: Toutes les transactions
                const startTime = Date.now();
                const allTransactions = await supabaseAPI.get('transactions');
                const endTime = Date.now();
                
                addResult(`✅ Toutes les transactions: ${allTransactions.length} (${endTime - startTime}ms)`, 'success');

                // Test 2: Transactions utilisateur
                const userStartTime = Date.now();
                const userTransactions = await supabaseAPI.getUserTransactions('<EMAIL>');
                const userEndTime = Date.now();
                
                addResult(`✅ Transactions utilisateur: ${userTransactions.length} (${userEndTime - userStartTime}ms)`, 'success');

                // Afficher les transactions
                if (userTransactions.length > 0) {
                    userTransactions.forEach((tx, i) => {
                        addResult(`${i+1}. ${tx.type} - ${tx.amount}€ - ${tx.status}`, 'info');
                    });
                }

            } catch (error) {
                addResult(`❌ Erreur: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }

        async function testDetailed() {
            clearResults();
            addResult('🔍 Test détaillé en cours...', 'info');

            try {
                // Test direct avec fetch
                const url = `${supabaseConfig.url}/rest/v1/transactions?user_email=<EMAIL>`;
                addResult(`URL: ${url}`, 'info');

                const response = await fetch(url, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                addResult(`Status: ${response.status}`, response.ok ? 'success' : 'error');

                if (response.ok) {
                    const data = await response.json();
                    addResult(`Données: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    const errorText = await response.text();
                    addResult(`Erreur: ${errorText}`, 'error');
                }

            } catch (error) {
                addResult(`❌ Erreur fetch: ${error.message}`, 'error');
            }
        }

        // Test automatique
        document.addEventListener('DOMContentLoaded', () => {
            addResult('Page chargée. Cliquez sur "Test Rapide" pour commencer.', 'info');
        });
    </script>
</body>
</html>
