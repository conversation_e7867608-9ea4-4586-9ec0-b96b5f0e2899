<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - CryptoBoost Terminal</title>
    <meta name="description" content="Rejoignez CryptoBoost - La plateforme de trading crypto la plus avancée">
    
    <!-- Font<PERSON> Futuristes -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700;800&family=Fira+Code:wght@300;400;500&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/cryptoboost-design-system.css">
    <link rel="stylesheet" href="assets/css/futuristic-auth.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body class="auth-body">
    <!-- Cyber Grid Background -->
    <div class="cyber-grid"></div>
    
    <!-- Matrix Rain Effect -->
    <div class="matrix-rain" id="matrix-rain"></div>
    
    <!-- Auth Particles -->
    <div class="auth-particles" id="auth-particles"></div>

    <!-- Registration Container -->
    <div class="auth-container">
        <!-- Branding Panel -->
        <div class="auth-branding">
            <div class="branding-content">
                <div class="brand-logo-large">
                    <i class="fas fa-rocket"></i>
                    <div class="logo-orbit">
                        <div class="orbit-ring"></div>
                        <div class="orbit-ring"></div>
                        <div class="orbit-ring"></div>
                    </div>
                </div>
                
                <h1 class="brand-title">CryptoBoost</h1>
                <p class="brand-subtitle">TRADING TERMINAL</p>
                
                <div class="brand-features">
                    <div class="feature-item">
                        <i class="fas fa-brain"></i>
                        <span>IA Trading Avancée</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Sécurité Maximale</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics Pro</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-rocket"></i>
                        <span>Performance Optimale</span>
                    </div>
                </div>
                
                <div class="live-stats">
                    <div class="stat-display">
                        <div class="stat-label">Traders Actifs</div>
                        <div class="stat-value" id="online-traders">45,892</div>
                    </div>
                    <div class="stat-display">
                        <div class="stat-label">Volume 24h</div>
                        <div class="stat-value" id="daily-volume">€2.4M</div>
                    </div>
                </div>
            </div>
            
            <div class="branding-visual">
                <div class="hologram-effect">
                    <div class="hologram-layer"></div>
                    <div class="hologram-layer"></div>
                    <div class="hologram-layer"></div>
                </div>
            </div>
        </div>

        <!-- Registration Panel -->
        <div class="auth-panel">
            <div class="auth-header">
                <h2 class="auth-title">Inscription</h2>
                <p class="auth-subtitle">Créez votre compte trader en 2 minutes</p>
                <div class="security-indicator">
                    <div class="security-dot"></div>
                    <span>Connexion Sécurisée</span>
                </div>
            </div>

            <form class="auth-form" id="register-form">
                <!-- Personal Information -->
                <div class="form-section">
                    <h3 class="section-title">Informations Personnelles</h3>
                    
                    <div class="form-row">
                        <div class="input-container">
                            <input type="text" id="firstName" name="firstName" class="cyber-input" placeholder="Prénom" required>
                            <div class="input-glow"></div>
                        </div>
                        <div class="input-container">
                            <input type="text" id="lastName" name="lastName" class="cyber-input" placeholder="Nom" required>
                            <div class="input-glow"></div>
                        </div>
                    </div>
                    
                    <div class="input-container">
                        <input type="email" id="email" name="email" class="cyber-input" placeholder="Adresse email" required>
                        <div class="input-glow"></div>
                    </div>
                    
                    <div class="input-container">
                        <input type="tel" id="phone" name="phone" class="cyber-input" placeholder="Téléphone (optionnel)">
                        <div class="input-glow"></div>
                    </div>
                </div>

                <!-- Security -->
                <div class="form-section">
                    <h3 class="section-title">Sécurité</h3>
                    
                    <div class="input-container">
                        <input type="password" id="password" name="password" class="cyber-input" placeholder="Mot de passe" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="input-glow"></div>
                    </div>
                    
                    <div class="input-container">
                        <input type="password" id="confirmPassword" name="confirmPassword" class="cyber-input" placeholder="Confirmer le mot de passe" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <div class="input-glow"></div>
                    </div>
                    
                    <div class="password-strength" id="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <div class="strength-text">Force du mot de passe</div>
                    </div>
                </div>

                <!-- Plan Selection -->
                <div class="form-section" id="plan-selection">
                    <h3 class="section-title">Choisissez votre Plan</h3>
                    
                    <div class="plan-options">
                        <label class="plan-option">
                            <input type="radio" name="plan" value="starter" checked>
                            <div class="plan-card-mini">
                                <div class="plan-icon"><i class="fas fa-seedling"></i></div>
                                <div class="plan-info">
                                    <div class="plan-name">STARTER</div>
                                    <div class="plan-price">€100 min</div>
                                    <div class="plan-roi">5-8% mensuel</div>
                                </div>
                            </div>
                        </label>
                        
                        <label class="plan-option">
                            <input type="radio" name="plan" value="pro">
                            <div class="plan-card-mini featured">
                                <div class="plan-badge">POPULAIRE</div>
                                <div class="plan-icon"><i class="fas fa-rocket"></i></div>
                                <div class="plan-info">
                                    <div class="plan-name">PRO</div>
                                    <div class="plan-price">€1,000 min</div>
                                    <div class="plan-roi">8-12% mensuel</div>
                                </div>
                            </div>
                        </label>
                        
                        <label class="plan-option">
                            <input type="radio" name="plan" value="elite">
                            <div class="plan-card-mini">
                                <div class="plan-icon"><i class="fas fa-crown"></i></div>
                                <div class="plan-info">
                                    <div class="plan-name">ELITE</div>
                                    <div class="plan-price">€10,000 min</div>
                                    <div class="plan-roi">12-18% mensuel</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="terms" name="terms" required>
                        <div class="checkmark"></div>
                        <span class="checkbox-label">
                            J'accepte les <a href="#terms" class="link">conditions d'utilisation</a> et la <a href="#privacy" class="link">politique de confidentialité</a>
                        </span>
                    </label>
                    
                    <label class="checkbox-container">
                        <input type="checkbox" id="newsletter" name="newsletter">
                        <div class="checkmark"></div>
                        <span class="checkbox-label">
                            Recevoir les actualités et conseils trading
                        </span>
                    </label>
                </div>

                <!-- Submit Button -->
                <button type="submit" class="btn btn-primary btn-xl auth-submit" id="register-btn">
                    <i class="fas fa-rocket"></i>
                    <span>Créer mon Compte</span>
                    <div class="btn-scan-line"></div>
                    <div class="btn-particles"></div>
                </button>
            </form>

            <!-- Alternative Registration -->
            <div class="auth-divider">
                <span>ou</span>
            </div>

            <button class="btn btn-outline btn-xl" id="google-register">
                <i class="fab fa-google"></i>
                <span>S'inscrire avec Google</span>
            </button>

            <!-- Auth Footer -->
            <div class="auth-footer">
                <p>Vous avez déjà un compte ?</p>
                <a href="login-modern.html" class="register-link">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Se Connecter</span>
                </a>
                
                <div class="back-to-home">
                    <a href="index-modern.html" class="home-link">
                        <i class="fas fa-arrow-left"></i>
                        <span>Retour à l'accueil</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Boot Sequence -->
    <div class="boot-sequence" id="boot-sequence">
        <div class="boot-content">
            <div class="boot-logo">
                <i class="fas fa-rocket"></i>
            </div>
            <div class="boot-text">
                <div class="boot-line">Initialisation du terminal...</div>
                <div class="boot-line">Connexion sécurisée établie...</div>
                <div class="boot-line">Chargement des modules IA...</div>
                <div class="boot-line">Système prêt !</div>
            </div>
            <div class="boot-progress">
                <div class="progress-bar" id="boot-progress-bar"></div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script src="assets/js/modern-auth.js"></script>
    <script src="assets/js/futuristic-effects.js"></script>
    
    <script>
        // Registration specific functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Registration page loaded');
            
            // Check for plan parameter
            const urlParams = new URLSearchParams(window.location.search);
            const selectedPlan = urlParams.get('plan');
            
            if (selectedPlan) {
                const planRadio = document.querySelector(`input[name="plan"][value="${selectedPlan}"]`);
                if (planRadio) {
                    planRadio.checked = true;
                    console.log(`📋 Plan ${selectedPlan} pre-selected`);
                }
            }
            
            // Password strength indicator
            const passwordInput = document.getElementById('password');
            const strengthIndicator = document.getElementById('password-strength');
            
            passwordInput.addEventListener('input', function() {
                updatePasswordStrength(this.value);
            });
            
            // Form validation
            const form = document.getElementById('register-form');
            form.addEventListener('submit', handleRegistration);
            
            // Google registration
            document.getElementById('google-register').addEventListener('click', handleGoogleRegistration);
        });
        
        function updatePasswordStrength(password) {
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');
            
            let strength = 0;
            let text = 'Très faible';
            let color = '#ef4444';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    text = 'Très faible';
                    color = '#ef4444';
                    break;
                case 2:
                    text = 'Faible';
                    color = '#f59e0b';
                    break;
                case 3:
                    text = 'Moyen';
                    color = '#eab308';
                    break;
                case 4:
                    text = 'Fort';
                    color = '#22c55e';
                    break;
                case 5:
                    text = 'Très fort';
                    color = '#10b981';
                    break;
            }
            
            strengthBar.style.width = `${(strength / 5) * 100}%`;
            strengthBar.style.background = color;
            strengthText.textContent = text;
            strengthText.style.color = color;
        }
        
        async function handleRegistration(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // Validation
            if (data.password !== data.confirmPassword) {
                alert('Les mots de passe ne correspondent pas');
                return;
            }
            
            if (!data.terms) {
                alert('Vous devez accepter les conditions d\'utilisation');
                return;
            }
            
            try {
                // Show boot sequence
                document.getElementById('boot-sequence').classList.add('active');
                
                // Register with Supabase
                const result = await window.ModernAuth.register(data);
                
                if (result.success) {
                    console.log('✅ Registration successful');
                    
                    // Redirect based on plan
                    setTimeout(() => {
                        if (data.plan === 'starter' || data.plan === 'pro') {
                            window.location.href = 'dashboard/home-modern.html';
                        } else {
                            window.location.href = 'dashboard/home-modern.html?welcome=true';
                        }
                    }, 3000);
                } else {
                    throw new Error(result.error);
                }
                
            } catch (error) {
                console.error('❌ Registration error:', error);
                document.getElementById('boot-sequence').classList.remove('active');
                alert('Erreur lors de l\'inscription: ' + error.message);
            }
        }
        
        async function handleGoogleRegistration() {
            try {
                const result = await window.ModernAuth.loginWithGoogle();
                if (result.success) {
                    window.location.href = 'dashboard/home-modern.html?welcome=true';
                }
            } catch (error) {
                console.error('❌ Google registration error:', error);
                alert('Erreur lors de l\'inscription Google: ' + error.message);
            }
        }
        
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }
    </script>
</body>
</html>
