<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inscription - CryptoBoost</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Montserrat:wght@700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="assets/css/modern-style.css">
</head>
<body>
    <div class="background-glow"></div>

    <main class="auth-container">
        <div class="auth-card" data-aos="fade-up">
            <a href="index.html" class="logo">CryptoBoost</a>
            <h1>Créez votre compte</h1>
            <p class="subtitle">Rejoignez la révolution de l'investissement par IA.</p>
            
            <form id="register-form">
                <div class="form-group">
                    <label for="name">Nom complet</label>
                    <input type="text" id="name" class="form-control" required placeholder="John Doe">
                </div>
                <div class="form-group">
                    <label for="email">Adresse e-mail</label>
                    <input type="email" id="email" class="form-control" required placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="password">Mot de passe</label>
                    <input type="password" id="password" class="form-control" required placeholder="••••••••">
                </div>
                <button type="submit" class="btn btn-primary">Créer mon compte</button>
            </form>
            
            <div class="auth-footer">
                <p>Déjà un compte ? <a href="login.html">Connectez-vous</a></p>
            </div>
        </div>
    </main>

    <script src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script>
      AOS.init({
          duration: 800,
          once: true,
      });
    </script>

    <!-- Scripts d'authentification -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="config.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/auth.js"></script>
    <script>
        document.getElementById('register-form').addEventListener('submit', registerUser);
    </script>
</body>
</html>
