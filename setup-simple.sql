-- Simple setup for CryptoBoost without <PERSON><PERSON> for testing
-- Execute this in Supabase SQL Editor

-- Drop existing tables if they exist
DROP TABLE IF EXISTS transactions CASCADE;
DROP TABLE IF EXISTS company_wallets CASCADE;
DROP TABLE IF EXISTS investment_plans CASCADE;
DROP TABLE IF EXISTS config CASCAD<PERSON>;
DROP TABLE IF EXISTS users CASCADE;

-- Create users table
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('user', 'admin')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    balance DECIMAL(15,2) DEFAULT 0.00,
    total_invested DECIMAL(15,2) DEFAULT 0.00,
    total_profit DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create investment plans table
CREATE TABLE investment_plans (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    investment VARCHAR(100) NOT NULL,
    description TEXT,
    roi_percentage DECIMAL(5,2),
    duration_days INTEGER,
    min_amount DECIMAL(15,2),
    max_amount DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create transactions table
CREATE TABLE transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_email VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('deposit', 'withdrawal', 'investment', 'profit')),
    amount DECIMAL(15,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'completed')),
    plan_name VARCHAR(255),
    transaction_hash VARCHAR(255),
    wallet_address VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create company wallets table
CREATE TABLE company_wallets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    currency VARCHAR(50) NOT NULL,
    address VARCHAR(255) NOT NULL,
    network VARCHAR(100),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create config table
CREATE TABLE config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable RLS for testing (enable later for production)
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE investment_plans DISABLE ROW LEVEL SECURITY;
ALTER TABLE transactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE company_wallets DISABLE ROW LEVEL SECURITY;
ALTER TABLE config DISABLE ROW LEVEL SECURITY;

-- Insert test data
-- Test users
INSERT INTO users (email, password, full_name, role, status, balance, total_invested, total_profit) VALUES
('<EMAIL>', 'admin123', 'Administrator', 'admin', 'active', 0.00, 0.00, 0.00),
('<EMAIL>', 'user123', 'Test User', 'user', 'active', 1000.00, 500.00, 50.00);

-- Investment plans
INSERT INTO investment_plans (name, investment, description, roi_percentage, duration_days, min_amount, max_amount) VALUES
('Starter', '100-1000€', 'Plan débutant avec 5% de ROI mensuel', 5.00, 30, 100.00, 1000.00),
('Premium', '1000-10000€', 'Plan premium avec 8% de ROI mensuel', 8.00, 60, 1000.00, 10000.00),
('VIP', '10000-100000€', 'Plan VIP avec 12% de ROI mensuel', 12.00, 90, 10000.00, 100000.00);

-- Company wallets
INSERT INTO company_wallets (currency, address, network) VALUES
('BTC', '**********************************', 'Bitcoin'),
('ETH', '0x742d35Cc6634C0532925a3b8D4C9db96C4b4d8b', 'Ethereum'),
('USDT', 'TQn9Y2khEsLJW1ChVWFMSMeRDow5oREqjK', 'Tron'),
('LTC', 'LQTpS1kxcr9fBiXzVhHBaVqeHYVrFe8182', 'Litecoin');

-- Sample transactions
INSERT INTO transactions (user_email, type, amount, status, plan_name, notes) VALUES
('<EMAIL>', 'deposit', 1000.00, 'completed', NULL, 'Initial deposit'),
('<EMAIL>', 'investment', 500.00, 'active', 'Starter', 'Investment in Starter plan'),
('<EMAIL>', 'profit', 25.00, 'completed', 'Starter', 'Monthly profit from Starter plan');

-- Configuration entries
INSERT INTO config (key, value, description) VALUES
('app_name', 'CryptoBoost', 'Application name'),
('maintenance_mode', 'false', 'Maintenance mode status'),
('min_deposit', '100', 'Minimum deposit amount'),
('max_deposit', '100000', 'Maximum deposit amount');

-- Grant permissions for anon role
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon;

SELECT 'Database setup completed successfully!' as status;
