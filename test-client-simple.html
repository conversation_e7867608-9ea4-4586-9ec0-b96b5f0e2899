<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Client.js Simple</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Client.js Simple</h1>
        
        <button onclick="testBasic()">Test de Base</button>
        <button onclick="testFunctions()">Test Fonctions</button>
        <button onclick="clearResults()">Effacer</button>
        
        <div id="results"></div>
    </div>

    <script>
        console.log('1. Script de test chargé');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testBasic() {
            clearResults();
            addResult('Test de base en cours...', 'info');
            
            // Test 1: Vérifier que le script de test fonctionne
            addResult('✅ Script de test: OK', 'success');
            
            // Test 2: Vérifier console
            console.log('2. Test de base exécuté');
            addResult('✅ Console: OK', 'success');
        }

        function testFunctions() {
            clearResults();
            addResult('Test des fonctions en cours...', 'info');
            
            // Test des fonctions une par une
            const functionsToTest = [
                'loadPublicPlans',
                'navigateTo', 
                'renderAppLayout',
                'loadHomePage',
                'invest',
                'logoutUser'
            ];
            
            functionsToTest.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult(`✅ ${funcName}: Disponible`, 'success');
                } else {
                    addResult(`❌ ${funcName}: Manquante (type: ${typeof window[funcName]})`, 'error');
                }
            });
            
            // Test des objets
            const objectsToTest = ['supabaseAPI', 'supabaseConfig'];
            objectsToTest.forEach(objName => {
                if (typeof window[objName] !== 'undefined') {
                    addResult(`✅ ${objName}: Disponible`, 'success');
                } else {
                    addResult(`❌ ${objName}: Manquant`, 'error');
                }
            });
        }

        // Test automatique
        document.addEventListener('DOMContentLoaded', () => {
            console.log('3. DOM chargé, avant chargement des scripts');
            addResult('Page chargée. Les scripts vont être chargés...', 'info');
        });
    </script>

    <!-- Chargement des scripts dans l'ordre -->
    <script src="config.js" onload="console.log('4. config.js chargé')" onerror="console.error('Erreur config.js')"></script>
    <script src="assets/js/supabase-api.js" onload="console.log('5. supabase-api.js chargé')" onerror="console.error('Erreur supabase-api.js')"></script>
    <script src="assets/js/client.js" onload="console.log('6. client.js chargé'); setTimeout(testFunctions, 100);" onerror="console.error('Erreur client.js')"></script>
</body>
</html>
