<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CryptoBoost - Test de Fonctionnalité</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0; }
        .error { background: #fee2e2; color: #991b1b; border: 1px solid #fca5a5; }
        .info { background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd; }
        .warning { background: #fef3c7; color: #92400e; border: 1px solid #fcd34d; }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover { background: #4338ca; }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .login-form {
            background: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        pre {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 CryptoBoost - Test de Fonctionnalité</h1>
        <p>Tests interactifs pour vérifier les fonctionnalités principales de l'application.</p>
        
        <div class="test-section">
            <h3>🔌 Test de l'API</h3>
            <button onclick="testAPI()">Tester l'API Supabase</button>
            <div id="apiResults"></div>
        </div>

        <div class="test-section">
            <h3>🔐 Test d'Authentification</h3>
            <div class="login-form">
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="testEmail" value="<EMAIL>" placeholder="Email de test">
                </div>
                <div class="form-group">
                    <label>Mot de passe:</label>
                    <input type="password" id="testPassword" value="user123" placeholder="Mot de passe">
                </div>
                <button onclick="testLogin()">Tester la Connexion</button>
                <button onclick="testRegister()">Tester l'Inscription</button>
            </div>
            <div id="authResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test des Plans d'Investissement</h3>
            <button onclick="testInvestmentPlans()">Charger les Plans</button>
            <div id="plansResults"></div>
        </div>

        <div class="test-section">
            <h3>💳 Test des Portefeuilles</h3>
            <button onclick="testWallets()">Charger les Portefeuilles</button>
            <div id="walletsResults"></div>
        </div>

        <div class="test-section">
            <h3>📈 Test des Transactions</h3>
            <button onclick="testTransactions()">Charger les Transactions</button>
            <div id="transactionsResults"></div>
        </div>

        <div class="test-section">
            <h3>🎯 Test Complet</h3>
            <button onclick="runAllTests()">🚀 Exécuter Tous les Tests</button>
            <button onclick="clearAllResults()">🧹 Effacer les Résultats</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script src="assets/js/auth.js"></script>
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function clearAllResults() {
            ['apiResults', 'authResults', 'plansResults', 'walletsResults', 'transactionsResults'].forEach(clearResults);
        }

        async function testAPI() {
            clearResults('apiResults');
            addResult('apiResults', '🔄 Test de l\'API en cours...', 'info');

            try {
                // Test basic connection
                const response = await fetch(`${supabaseConfig.url}/rest/v1/`, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`
                    }
                });

                if (response.ok) {
                    addResult('apiResults', '✅ Connexion API: OK', 'success');
                } else {
                    addResult('apiResults', `❌ Connexion API: Erreur ${response.status}`, 'error');
                }

                // Test supabaseAPI instance
                if (typeof supabaseAPI !== 'undefined') {
                    addResult('apiResults', '✅ Instance supabaseAPI: Chargée', 'success');
                } else {
                    addResult('apiResults', '❌ Instance supabaseAPI: Non trouvée', 'error');
                }

            } catch (error) {
                addResult('apiResults', `❌ Erreur API: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            clearResults('authResults');
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;

            addResult('authResults', `🔄 Test de connexion pour ${email}...`, 'info');

            try {
                const users = await supabaseAPI.getUserByEmail(email);
                if (users && users.length > 0) {
                    const user = users.find(u => u.password === password);
                    if (user) {
                        addResult('authResults', `✅ Connexion réussie: ${user.full_name} (${user.role})`, 'success');
                        addResult('authResults', `<pre>${JSON.stringify(user, null, 2)}</pre>`, 'info');
                    } else {
                        addResult('authResults', '❌ Mot de passe incorrect', 'error');
                    }
                } else {
                    addResult('authResults', '❌ Utilisateur non trouvé', 'error');
                }
            } catch (error) {
                addResult('authResults', `❌ Erreur de connexion: ${error.message}`, 'error');
            }
        }

        async function testRegister() {
            clearResults('authResults');
            addResult('authResults', '🔄 Test d\'inscription...', 'info');

            const testUser = {
                email: `test${Date.now()}@cryptoboost.com`,
                password: 'test123',
                full_name: 'Test User',
                role: 'client'
            };

            try {
                const result = await supabaseAPI.createUser(testUser);
                if (result && result.length > 0) {
                    addResult('authResults', `✅ Inscription réussie: ${testUser.email}`, 'success');
                } else {
                    addResult('authResults', '❌ Échec de l\'inscription', 'error');
                }
            } catch (error) {
                addResult('authResults', `❌ Erreur d\'inscription: ${error.message}`, 'error');
            }
        }

        async function testInvestmentPlans() {
            clearResults('plansResults');
            addResult('plansResults', '🔄 Chargement des plans d\'investissement...', 'info');

            try {
                const plans = await supabaseAPI.getInvestmentPlans();
                if (plans && plans.length > 0) {
                    addResult('plansResults', `✅ ${plans.length} plans d'investissement trouvés`, 'success');
                    plans.forEach(plan => {
                        addResult('plansResults', 
                            `📊 ${plan.name}: ${plan.roi_percentage}% ROI, ${plan.min_amount}-${plan.max_amount}€`, 
                            'info');
                    });
                } else {
                    addResult('plansResults', '⚠️ Aucun plan d\'investissement trouvé', 'warning');
                }
            } catch (error) {
                addResult('plansResults', `❌ Erreur: ${error.message}`, 'error');
            }
        }

        async function testWallets() {
            clearResults('walletsResults');
            addResult('walletsResults', '🔄 Chargement des portefeuilles...', 'info');

            try {
                const wallets = await supabaseAPI.getCompanyWallets();
                if (wallets && wallets.length > 0) {
                    addResult('walletsResults', `✅ ${wallets.length} portefeuilles trouvés`, 'success');
                    wallets.forEach(wallet => {
                        addResult('walletsResults', 
                            `💰 ${wallet.currency} (${wallet.symbol}): ${wallet.address.substring(0, 20)}...`, 
                            'info');
                    });
                } else {
                    addResult('walletsResults', '⚠️ Aucun portefeuille trouvé', 'warning');
                }
            } catch (error) {
                addResult('walletsResults', `❌ Erreur: ${error.message}`, 'error');
            }
        }

        async function testTransactions() {
            clearResults('transactionsResults');
            addResult('transactionsResults', '🔄 Chargement des transactions...', 'info');

            try {
                const transactions = await supabaseAPI.getUserTransactions('<EMAIL>');
                if (transactions && transactions.length > 0) {
                    addResult('transactionsResults', `✅ ${transactions.length} transactions trouvées`, 'success');
                    transactions.forEach(tx => {
                        addResult('transactionsResults', 
                            `💳 ${tx.type}: ${tx.amount}€ - ${tx.status}`, 
                            'info');
                    });
                } else {
                    addResult('transactionsResults', '⚠️ Aucune transaction trouvée', 'warning');
                }
            } catch (error) {
                addResult('transactionsResults', `❌ Erreur: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearAllResults();
            await testAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testInvestmentPlans();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testWallets();
            await new Promise(resolve => setTimeout(resolve, 500));
            await testTransactions();
        }
    </script>
</body>
</html>
