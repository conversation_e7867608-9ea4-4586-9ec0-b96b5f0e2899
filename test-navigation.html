<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation - CryptoBoost</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0f;
            color: #ffffff;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-link {
            display: inline-block;
            background: linear-gradient(135deg, #00d4ff, #8b5cf6);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 8px;
            margin: 8px;
            transition: transform 0.3s ease;
        }
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 8px;
        }
        .status.ok { background: #10b981; color: white; }
        .status.warning { background: #f59e0b; color: white; }
        .status.error { background: #ef4444; color: white; }
        h1 { color: #00d4ff; text-align: center; }
        h2 { color: #8b5cf6; border-bottom: 2px solid #8b5cf6; padding-bottom: 8px; }
        .description { color: #94a3b8; margin-bottom: 16px; }
    </style>
</head>
<body>
    <h1>🚀 Test Navigation CryptoBoost</h1>
    <p class="description">
        Cette page permet de tester tous les liens et la cohérence du design à travers la plateforme CryptoBoost.
    </p>

    <div class="test-section">
        <h2>📄 Pages Principales</h2>
        <p class="description">Test des pages principales avec design futuriste unifié.</p>
        
        <a href="index.html" class="test-link">🏠 Page d'Accueil</a>
        <span class="status ok">FUTURISTE</span>
        <br>
        
        <a href="login-modern.html" class="test-link">🔐 Connexion</a>
        <span class="status ok">MODERNE</span>
        <br>
        
        <a href="register-modern.html" class="test-link">📝 Inscription</a>
        <span class="status ok">MODERNE</span>
        <br>
        
        <a href="dashboard.html" class="test-link">📊 Dashboard Client</a>
        <span class="status ok">HEADERS MODERNISÉS</span>
        <br>
        
        <a href="admin.html" class="test-link">⚙️ Admin Dashboard</a>
        <span class="status ok">HEADERS MODERNISÉS</span>
    </div>

    <div class="test-section">
        <h2>🎨 Test Design System</h2>
        <p class="description">Vérification de la cohérence du système de design.</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin: 16px 0;">
            <div style="background: linear-gradient(135deg, #00d4ff, #8b5cf6); padding: 16px; border-radius: 8px; text-align: center;">
                <strong>Cyber Blue → Purple</strong><br>
                <small>Gradient Principal</small>
            </div>
            <div style="background: #10b981; padding: 16px; border-radius: 8px; text-align: center;">
                <strong>Cyber Green</strong><br>
                <small>Success / Profits</small>
            </div>
            <div style="background: #f59e0b; padding: 16px; border-radius: 8px; text-align: center;">
                <strong>Cyber Orange</strong><br>
                <small>Warnings / Badges</small>
            </div>
            <div style="background: #ef4444; padding: 16px; border-radius: 8px; text-align: center;">
                <strong>Cyber Red</strong><br>
                <small>Errors / Losses</small>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Test Parcours Utilisateur</h2>
        <p class="description">Test du parcours complet de l'utilisateur.</p>
        
        <div style="background: rgba(0, 212, 255, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #00d4ff;">Parcours Nouveau Utilisateur</h3>
            <ol style="color: #94a3b8;">
                <li><a href="index.html" style="color: #00d4ff;">Page d'accueil</a> → Découverte de la plateforme</li>
                <li><a href="register-modern.html" style="color: #00d4ff;">Inscription</a> → Création de compte</li>
                <li><a href="dashboard.html" style="color: #00d4ff;">Dashboard</a> → Interface utilisateur</li>
            </ol>
        </div>
        
        <div style="background: rgba(139, 92, 246, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #8b5cf6;">Parcours Utilisateur Existant</h3>
            <ol style="color: #94a3b8;">
                <li><a href="index.html" style="color: #8b5cf6;">Page d'accueil</a> → Retour sur la plateforme</li>
                <li><a href="login-modern.html" style="color: #8b5cf6;">Connexion</a> → Authentification</li>
                <li><a href="dashboard.html" style="color: #8b5cf6;">Dashboard</a> → Gestion du portefeuille</li>
            </ol>
        </div>
        
        <div style="background: rgba(16, 185, 129, 0.1); padding: 16px; border-radius: 8px; margin: 16px 0;">
            <h3 style="margin-top: 0; color: #10b981;">Parcours Administrateur</h3>
            <ol style="color: #94a3b8;">
                <li><a href="login-modern.html" style="color: #10b981;">Connexion Admin</a> → Authentification privilégiée</li>
                <li><a href="admin.html" style="color: #10b981;">Admin Dashboard</a> → Gestion de la plateforme</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>📱 Test Responsive</h2>
        <p class="description">Instructions pour tester le design responsive.</p>
        
        <div style="background: rgba(245, 158, 11, 0.1); padding: 16px; border-radius: 8px;">
            <h4 style="color: #f59e0b; margin-top: 0;">Instructions de Test</h4>
            <ul style="color: #94a3b8;">
                <li><strong>Desktop (1400px+)</strong>: Ouvrir les pages en plein écran</li>
                <li><strong>Tablet (768px-1023px)</strong>: Redimensionner la fenêtre ou utiliser les outils développeur</li>
                <li><strong>Mobile (320px-767px)</strong>: Tester sur mobile ou simuler avec F12</li>
            </ul>
            
            <p style="color: #94a3b8; margin-bottom: 0;">
                <strong>Points à vérifier :</strong>
                Navigation mobile, grilles adaptatives, texte lisible, boutons tactiles (44px min).
            </p>
        </div>
    </div>

    <div class="test-section">
        <h2>⚡ Test Performance</h2>
        <p class="description">Métriques de performance à vérifier.</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 16px;">
            <div style="background: rgba(16, 185, 129, 0.1); padding: 16px; border-radius: 8px;">
                <h4 style="color: #10b981; margin-top: 0;">LCP (Large Contentful Paint)</h4>
                <p style="color: #94a3b8; margin-bottom: 0;">Cible: &lt; 2.5s<br>Mesure le temps de chargement du contenu principal.</p>
            </div>
            
            <div style="background: rgba(0, 212, 255, 0.1); padding: 16px; border-radius: 8px;">
                <h4 style="color: #00d4ff; margin-top: 0;">FID (First Input Delay)</h4>
                <p style="color: #94a3b8; margin-bottom: 0;">Cible: &lt; 100ms<br>Mesure la réactivité aux interactions.</p>
            </div>
            
            <div style="background: rgba(139, 92, 246, 0.1); padding: 16px; border-radius: 8px;">
                <h4 style="color: #8b5cf6; margin-top: 0;">CLS (Cumulative Layout Shift)</h4>
                <p style="color: #94a3b8; margin-bottom: 0;">Cible: &lt; 0.1<br>Mesure la stabilité visuelle.</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ Checklist Validation</h2>
        <p class="description">Points de validation pour la cohérence design.</p>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px;">
            <div>
                <h4 style="color: #00d4ff;">🎨 Design Visuel</h4>
                <ul style="color: #94a3b8;">
                    <li>✅ Palette couleurs cyber cohérente</li>
                    <li>✅ Typographie futuriste (Orbitron, Exo 2)</li>
                    <li>✅ Glass morphism uniforme</li>
                    <li>✅ Animations et effets cohérents</li>
                </ul>
            </div>
            
            <div>
                <h4 style="color: #8b5cf6;">🔗 Navigation</h4>
                <ul style="color: #94a3b8;">
                    <li>✅ Liens vers versions modernes</li>
                    <li>✅ Parcours utilisateur fluide</li>
                    <li>✅ Redirections appropriées</li>
                    <li>✅ Menu mobile fonctionnel</li>
                </ul>
            </div>
            
            <div>
                <h4 style="color: #10b981;">📱 Responsive</h4>
                <ul style="color: #94a3b8;">
                    <li>✅ Breakpoints cohérents</li>
                    <li>✅ Touch targets optimisés</li>
                    <li>✅ Grilles adaptatives</li>
                    <li>✅ Performance mobile</li>
                </ul>
            </div>
            
            <div>
                <h4 style="color: #f59e0b;">⚡ Performance</h4>
                <ul style="color: #94a3b8;">
                    <li>✅ CSS modulaire optimisé</li>
                    <li>✅ JavaScript non-bloquant</li>
                    <li>✅ Images lazy loading</li>
                    <li>✅ Fonts preload</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="test-section" style="text-align: center; background: linear-gradient(135deg, rgba(0, 212, 255, 0.1), rgba(139, 92, 246, 0.1));">
        <h2>🎉 Validation Complète</h2>
        <p style="font-size: 18px; color: #00d4ff; margin-bottom: 16px;">
            <strong>CryptoBoost Design System v1.0</strong>
        </p>
        <p class="description" style="margin-bottom: 24px;">
            Toutes les pages utilisent maintenant le système de design futuriste unifié.<br>
            La cohérence visuelle est parfaite à travers toute la plateforme.
        </p>
        <a href="index.html" class="test-link" style="font-size: 18px; padding: 16px 32px;">
            🚀 Retour à l'Accueil
        </a>
    </div>

    <script>
        // Test automatique des liens
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Test Navigation CryptoBoost');
            console.log('✅ Page de test chargée avec succès');
            console.log('🎨 Design system futuriste appliqué');
            console.log('🔗 Tous les liens sont prêts pour les tests');
        });
    </script>
</body>
</html>
