<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple Config</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Test Simple Config</h1>
        <div id="results"></div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        // Test 1: Configuration manuelle
        addResult('🧪 Test de configuration manuelle...', 'info');

        // Configuration Supabase directe
        const supabaseConfig = {
            url: 'https://misciubbwfasvyeoqwgq.supabase.co',
            anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pc2NpdWJid2Zhc3Z5ZW9xd2dxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NzcwMzMsImV4cCI6MjA2ODU1MzAzM30.oYxUXt776PAijkt1ziMBXQDnOHj-Q9uUjpdJdOgFRaM'
        };

        // Tables
        const tables = {
            users: 'users',
            transactions: 'transactions',
            config: 'config',
            plans: 'investment_plans',
            wallets: 'company_wallets'
        };

        // API endpoints
        const apiEndpoints = {
            users: `${supabaseConfig.url}/rest/v1/${tables.users}`,
            transactions: `${supabaseConfig.url}/rest/v1/${tables.transactions}`,
            config: `${supabaseConfig.url}/rest/v1/${tables.config}`,
            plans: `${supabaseConfig.url}/rest/v1/${tables.plans}`,
            wallets: `${supabaseConfig.url}/rest/v1/${tables.wallets}`
        };

        // Rendre globaux
        window.supabaseConfig = supabaseConfig;
        window.apiEndpoints = apiEndpoints;
        window.tables = tables;

        addResult('✅ Configuration créée manuellement', 'success');
        addResult(`<pre>supabaseConfig: ${JSON.stringify(supabaseConfig, null, 2)}</pre>`, 'info');

        // Test 2: Classe SupabaseAPI simplifiée
        class SimpleSupabaseAPI {
            constructor() {
                this.config = window.supabaseConfig;
                this.endpoints = window.apiEndpoints;
            }

            async request(url, options = {}) {
                try {
                    const response = await fetch(url, {
                        ...options,
                        headers: {
                            'apikey': this.config.anonKey,
                            'Authorization': `Bearer ${this.config.anonKey}`,
                            'Content-Type': 'application/json',
                            ...options.headers
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('API Request Error:', error);
                    throw error;
                }
            }

            async get(table) {
                return await this.request(this.endpoints[table]);
            }

            async getInvestmentPlans() {
                return await this.get('plans');
            }

            async getUserTransactions(email) {
                const url = `${this.endpoints.transactions}?user_email=eq.${encodeURIComponent(email)}`;
                return await this.request(url);
            }
        }

        // Créer instance
        const supabaseAPI = new SimpleSupabaseAPI();
        window.supabaseAPI = supabaseAPI;

        addResult('✅ SimpleSupabaseAPI créée', 'success');

        // Test 3: Test des appels API
        async function testAPI() {
            try {
                addResult('🧪 Test des appels API...', 'info');

                // Test 1: Users
                const users = await supabaseAPI.get('users');
                addResult(`✅ Users: ${users ? users.length : 0} enregistrements`, 'success');

                // Test 2: Plans
                const plans = await supabaseAPI.getInvestmentPlans();
                addResult(`✅ Plans: ${plans ? plans.length : 0} enregistrements`, 'success');

                // Test 3: Transactions
                const transactions = await supabaseAPI.getUserTransactions('<EMAIL>');
                addResult(`✅ Transactions: ${transactions ? transactions.length : 0} enregistrements`, 'success');

                addResult('🎉 Tous les tests API réussis !', 'success');

            } catch (error) {
                addResult(`❌ Erreur API: ${error.message}`, 'error');
            }
        }

        // Test automatique
        setTimeout(testAPI, 1000);

        // Vérification finale
        setTimeout(() => {
            addResult('🔍 Vérification finale des objets globaux...', 'info');
            
            if (window.supabaseConfig) {
                addResult('✅ window.supabaseConfig: Disponible', 'success');
            } else {
                addResult('❌ window.supabaseConfig: Manquant', 'error');
            }
            
            if (window.supabaseAPI) {
                addResult('✅ window.supabaseAPI: Disponible', 'success');
            } else {
                addResult('❌ window.supabaseAPI: Manquant', 'error');
            }
        }, 2000);
    </script>
</body>
</html>
