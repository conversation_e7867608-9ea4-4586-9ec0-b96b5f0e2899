# Test de connexion Supabase simple
# Script PowerShell pour tester la connexion à la base de données

Write-Host "Test de connexion Supabase - CryptoBoost" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Configuration Supabase
$supabaseUrl = "https://misciubbwfasvyeoqwgq.supabase.co"
$anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1pc2NpdWJid2Zhc3Z5ZW9xd2dxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI5NzcwMzMsImV4cCI6MjA2ODU1MzAzM30.oYxUXt776PAijkt1ziMBXQDnOHj-Q9uUjpdJdOgFRaM"

# Headers pour les requêtes
$headers = @{
    "apikey" = $anonKey
    "Authorization" = "Bearer $anonKey"
    "Content-Type" = "application/json"
}

Write-Host "Test de connexion de base..." -ForegroundColor Yellow

try {
    # Test de connexion de base
    $response = Invoke-RestMethod -Uri "$supabaseUrl/rest/v1/" -Headers $headers -Method GET
    Write-Host "SUCCES: Connexion Supabase OK" -ForegroundColor Green
    Write-Host "URL: $supabaseUrl" -ForegroundColor Blue
} catch {
    Write-Host "ERREUR: Connexion echouee" -ForegroundColor Red
    Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Test des tables..." -ForegroundColor Yellow

# Test des tables principales
$tables = @("users", "investment_plans", "transactions", "company_wallets", "config")

foreach ($table in $tables) {
    try {
        $url = "$supabaseUrl/rest/v1/$table"
        $data = Invoke-RestMethod -Uri $url -Headers $headers -Method GET
        $count = if ($data) { $data.Count } else { 0 }
        Write-Host "Table '$table': $count enregistrements" -ForegroundColor Green
    } catch {
        Write-Host "Table '$table': Erreur - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Test des utilisateurs..." -ForegroundColor Yellow

try {
    $users = Invoke-RestMethod -Uri "$supabaseUrl/rest/v1/users" -Headers $headers -Method GET
    if ($users -and $users.Count -gt 0) {
        Write-Host "Utilisateurs trouves: $($users.Count)" -ForegroundColor Green
        foreach ($user in $users) {
            Write-Host "  - $($user.email) ($($user.role))" -ForegroundColor Blue
        }
    } else {
        Write-Host "Aucun utilisateur trouve" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Erreur lors de la recuperation des utilisateurs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test termine!" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Pause pour voir les résultats
Write-Host "Appuyez sur une touche pour continuer..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
