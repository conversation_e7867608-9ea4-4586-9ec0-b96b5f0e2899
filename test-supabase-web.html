<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase - CryptoBoost</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 10px 5px;
        }
        button:hover {
            background: #4338ca;
        }
        .loading {
            color: #6b7280;
        }
        pre {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test de Connexion Supabase</h1>
        <p>Cet outil teste la connexion à la base de données Supabase et vérifie que toutes les tables sont accessibles.</p>
        
        <div class="test-controls">
            <button onclick="testConnection()">🔗 Tester la Connexion</button>
            <button onclick="testTables()">📊 Tester les Tables</button>
            <button onclick="testData()">📋 Tester les Données</button>
            <button onclick="runAllTests()">🚀 Exécuter Tous les Tests</button>
        </div>

        <div id="results"></div>
    </div>

    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    <script>
        const resultsDiv = document.getElementById('results');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
        }

        async function testConnection() {
            clearResults();
            addResult('🔄 Test de connexion en cours...', 'loading');

            try {
                // Test basic connection by trying to fetch from a simple endpoint
                const response = await fetch(`${supabaseConfig.url}/rest/v1/`, {
                    headers: {
                        'apikey': supabaseConfig.anonKey,
                        'Authorization': `Bearer ${supabaseConfig.anonKey}`
                    }
                });

                if (response.ok) {
                    addResult('✅ SUCCÈS: Connexion Supabase établie', 'success');
                    addResult(`📍 URL: ${supabaseConfig.url}`, 'info');
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                addResult(`❌ ERREUR: Connexion échouée - ${error.message}`, 'error');
            }
        }

        async function testTables() {
            addResult('🔄 Test des tables en cours...', 'loading');

            const tables = ['users', 'investment_plans', 'transactions', 'company_wallets', 'config'];
            
            for (const table of tables) {
                try {
                    const data = await supabaseAPI.get(table);
                    addResult(`✅ Table "${table}": ${data ? data.length : 0} enregistrements`, 'success');
                } catch (error) {
                    addResult(`❌ Table "${table}": ${error.message}`, 'error');
                }
            }
        }

        async function testData() {
            addResult('🔄 Test des données en cours...', 'loading');

            try {
                // Test users
                const users = await supabaseAPI.get('users');
                addResult(`👥 Utilisateurs: ${users ? users.length : 0}`, 'info');
                
                if (users && users.length > 0) {
                    addResult(`<pre>${JSON.stringify(users[0], null, 2)}</pre>`, 'info');
                }

                // Test investment plans
                const plans = await supabaseAPI.getInvestmentPlans();
                addResult(`📊 Plans d'investissement: ${plans ? plans.length : 0}`, 'info');

                // Test config
                const config = await supabaseAPI.getConfig();
                addResult(`⚙️ Configuration: ${config ? config.length : 0} entrées`, 'info');

            } catch (error) {
                addResult(`❌ Erreur lors du test des données: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 Exécution de tous les tests...', 'info');
            
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTables();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testData();
            
            addResult('✅ Tous les tests terminés!', 'success');
        }

        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', () => {
            addResult('🎯 Page de test chargée. Cliquez sur un bouton pour commencer.', 'info');
        });
    </script>
</body>
</html>
