<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Supabase Connection - CryptoBoost</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-header h1 {
            color: #1f2937;
            margin-bottom: 10px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
        }
        
        .test-section h3 {
            color: #374151;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6b7280;
        }
        
        .status-indicator.loading {
            background: #f59e0b;
            animation: pulse 1s infinite;
        }
        
        .status-indicator.success {
            background: #10b981;
        }
        
        .status-indicator.error {
            background: #ef4444;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .test-result {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 Test de Connexion Supabase</h1>
            <p>Vérification de l'intégration de la base de données CryptoBoost</p>
        </div>

        <!-- Configuration Test -->
        <div class="test-section">
            <h3>
                <div class="status-indicator" id="config-status"></div>
                Configuration
            </h3>
            <button class="test-button" onclick="testConfiguration()">Tester Configuration</button>
            <div class="test-result" id="config-result">En attente...</div>
        </div>

        <!-- Connection Test -->
        <div class="test-section">
            <h3>
                <div class="status-indicator" id="connection-status"></div>
                Connexion à la Base de Données
            </h3>
            <button class="test-button" onclick="testConnection()">Tester Connexion</button>
            <div class="test-result" id="connection-result">En attente...</div>
        </div>

        <!-- Authentication Test -->
        <div class="test-section">
            <h3>
                <div class="status-indicator" id="auth-status"></div>
                Authentification
            </h3>
            <button class="test-button" onclick="testAuthentication()">Tester Auth</button>
            <div class="test-result" id="auth-result">En attente...</div>
        </div>

        <!-- Database Operations Test -->
        <div class="test-section">
            <h3>
                <div class="status-indicator" id="db-status"></div>
                Opérations Base de Données
            </h3>
            <button class="test-button" onclick="testDatabaseOperations()">Tester DB</button>
            <div class="test-result" id="db-result">En attente...</div>
        </div>

        <!-- API Integration Test -->
        <div class="test-section">
            <h3>
                <div class="status-indicator" id="api-status"></div>
                Intégration API
            </h3>
            <button class="test-button" onclick="testAPIIntegration()">Tester API</button>
            <div class="test-result" id="api-result">En attente...</div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="test-button" onclick="runAllTests()" style="background: #059669; font-size: 16px; padding: 15px 30px;">
                🚀 Exécuter Tous les Tests
            </button>
        </div>
    </div>

    <!-- Scripts -->
    <script src="config.js"></script>
    <script src="assets/js/supabase-api.js"></script>
    
    <script>
        // Test functions
        async function testConfiguration() {
            updateStatus('config', 'loading');
            updateResult('config', 'Test de configuration en cours...\n');
            
            try {
                // Check if config is loaded
                if (typeof window.supabaseConfig === 'undefined') {
                    throw new Error('Configuration Supabase non trouvée');
                }
                
                const config = window.supabaseConfig;
                let result = 'Configuration Supabase:\n';
                result += `✅ URL: ${config.url}\n`;
                result += `✅ Anon Key: ${config.anonKey.substring(0, 20)}...\n`;
                result += `✅ Service Key: ${config.serviceKey ? 'Configurée' : 'Non configurée'}\n`;
                
                updateStatus('config', 'success');
                updateResult('config', result);
                
            } catch (error) {
                updateStatus('config', 'error');
                updateResult('config', `❌ Erreur: ${error.message}`);
            }
        }
        
        async function testConnection() {
            updateStatus('connection', 'loading');
            updateResult('connection', 'Test de connexion en cours...\n');
            
            try {
                // Wait for API to be ready
                if (!window.supabaseAPI) {
                    throw new Error('API Supabase non initialisée');
                }
                
                await waitForAPI();
                
                const isConnected = await window.supabaseAPI.testConnection();
                
                if (isConnected) {
                    updateStatus('connection', 'success');
                    updateResult('connection', '✅ Connexion à Supabase réussie!\nBase de données accessible.');
                } else {
                    throw new Error('Échec de la connexion');
                }
                
            } catch (error) {
                updateStatus('connection', 'error');
                updateResult('connection', `❌ Erreur de connexion: ${error.message}`);
            }
        }
        
        async function testAuthentication() {
            updateStatus('auth', 'loading');
            updateResult('auth', 'Test d\'authentification en cours...\n');
            
            try {
                await waitForAPI();
                
                // Test session check
                const session = await window.supabaseAPI.checkSession();
                
                let result = 'Test d\'authentification:\n';
                result += `✅ Vérification de session: ${session ? 'Session active' : 'Aucune session'}\n`;
                result += `✅ API Auth disponible: Oui\n`;
                
                updateStatus('auth', 'success');
                updateResult('auth', result);
                
            } catch (error) {
                updateStatus('auth', 'error');
                updateResult('auth', `❌ Erreur d'authentification: ${error.message}`);
            }
        }
        
        async function testDatabaseOperations() {
            updateStatus('db', 'loading');
            updateResult('db', 'Test des opérations base de données...\n');
            
            try {
                await waitForAPI();
                
                let result = 'Test des opérations DB:\n';
                
                // Test table access (this might fail if tables don't exist yet)
                try {
                    const metrics = await window.supabaseAPI.getAdminMetrics();
                    result += `✅ Lecture métriques: Succès\n`;
                    result += `   - Utilisateurs: ${metrics.totalUsers}\n`;
                    result += `   - Dépôts: €${metrics.totalDeposits}\n`;
                } catch (dbError) {
                    result += `⚠️ Lecture métriques: ${dbError.message}\n`;
                }
                
                updateStatus('db', 'success');
                updateResult('db', result);
                
            } catch (error) {
                updateStatus('db', 'error');
                updateResult('db', `❌ Erreur DB: ${error.message}`);
            }
        }
        
        async function testAPIIntegration() {
            updateStatus('api', 'loading');
            updateResult('api', 'Test de l\'intégration API...\n');
            
            try {
                await waitForAPI();
                
                let result = 'Test intégration API:\n';
                result += `✅ API initialisée: ${window.supabaseAPI.isReady()}\n`;
                result += `✅ Client Supabase: ${window.supabaseAPI.supabase ? 'Disponible' : 'Non disponible'}\n`;
                
                // Test API methods availability
                const methods = ['login', 'register', 'logout', 'getUserPortfolio', 'getAdminMetrics'];
                methods.forEach(method => {
                    const available = typeof window.supabaseAPI[method] === 'function';
                    result += `${available ? '✅' : '❌'} Méthode ${method}: ${available ? 'Disponible' : 'Manquante'}\n`;
                });
                
                updateStatus('api', 'success');
                updateResult('api', result);
                
            } catch (error) {
                updateStatus('api', 'error');
                updateResult('api', `❌ Erreur API: ${error.message}`);
            }
        }
        
        async function runAllTests() {
            console.log('🧪 Exécution de tous les tests...');
            
            await testConfiguration();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDatabaseOperations();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAPIIntegration();
            
            console.log('✅ Tous les tests terminés');
        }
        
        // Utility functions
        function updateStatus(test, status) {
            const indicator = document.getElementById(`${test}-status`);
            indicator.className = `status-indicator ${status}`;
        }
        
        function updateResult(test, result) {
            const resultElement = document.getElementById(`${test}-result`);
            resultElement.textContent = result;
        }
        
        async function waitForAPI() {
            let attempts = 0;
            while (attempts < 50 && (!window.supabaseAPI || !window.supabaseAPI.isReady())) {
                await new Promise(resolve => setTimeout(resolve, 100));
                attempts++;
            }
            
            if (!window.supabaseAPI || !window.supabaseAPI.isReady()) {
                throw new Error('API Supabase non disponible après 5 secondes');
            }
        }
        
        // Auto-run configuration test on load
        window.addEventListener('load', () => {
            setTimeout(testConfiguration, 1000);
        });
    </script>
</body>
</html>
